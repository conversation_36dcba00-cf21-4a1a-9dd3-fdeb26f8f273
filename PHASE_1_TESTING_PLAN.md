# Phase 1: Candidate Membership System - Testing Plan

## Overview
This document outlines the comprehensive testing plan for the Phase 1 implementation of the candidate membership system, which enforces a "1 free Visume" limit with upgrade messaging and WhatsApp contact integration.

## 🎯 Core Features Implemented

### Backend Components
1. **Membership Validation in Video Profile Creation**
   - Location: `visume-api/controllers/videoProfileController.js`
   - Function: `checkCandidateMembershipStatus()` and modified `createVideoProfile()`
   - Purpose: Enforces 1 free Visume limit before allowing creation

2. **Automatic Plan Assignment for New Candidates**
   - Location: `visume-api/controllers/userController.js`
   - Function: Modified candidate registration transaction
   - Purpose: Creates default "Free Candidate Plan" with 1 credit for new users

3. **Membership Status API Endpoint**
   - Location: `visume-api/routes/authRoutes.js` + `videoProfileController.js`
   - Endpoint: `GET /api/v1/candidate-membership/:cand_id`
   - Purpose: Provides membership status, usage count, and upgrade eligibility

### Frontend Components
1. **Membership Limit Modal**
   - Location: `visume-ui/src/views/candidate/components/MembershipLimitModal.jsx`
   - Purpose: Shows limit reached message with WhatsApp contact option

2. **Membership Service**
   - Location: `visume-ui/src/services/membershipService.js`
   - Purpose: Handles all membership-related API calls and validation

3. **Dashboard Integration**
   - Location: `visume-ui/src/views/candidate/Dashboard/`
   - Purpose: Displays membership status, usage progress, and upgrade prompts

4. **CreateVR Flow Modification**
   - Location: `visume-ui/src/views/candidate/components/CreateVR/CreateVR.jsx`
   - Purpose: Validates membership before allowing Visume creation

## 🧪 Test Scenarios

### 1. New Candidate Registration
**Test Case**: Verify new candidates get default plan
- **Steps**:
  1. Register a new candidate account
  2. Check database for `jobseekerplans` entry
  3. Verify plan has 1 credit assigned
- **Expected Result**: New candidate gets "Free Candidate Plan" with 1 credit
- **Database Query**: 
  ```sql
  SELECT jp.*, p.plan_name FROM jobseekerplans jp 
  JOIN plans p ON jp.plan_id = p.id 
  WHERE jp.cand_id = [NEW_CANDIDATE_ID];
  ```

### 2. First Visume Creation (Success Path)
**Test Case**: Candidate with 0 Visumes can create their first one
- **Steps**:
  1. Login as new candidate (0 Visumes created)
  2. Navigate to Create Visume
  3. Complete the creation process
  4. Verify Visume is created successfully
- **Expected Result**: Visume created, credit decremented to 0
- **API Endpoint**: `POST /api/v1/create-video-resume`

### 3. Second Visume Creation (Limit Reached)
**Test Case**: Candidate with 1 Visume cannot create another
- **Steps**:
  1. Login as candidate who already has 1 Visume
  2. Attempt to create another Visume
  3. Verify limit reached modal appears
  4. Test WhatsApp contact button
- **Expected Result**: 403 error, modal shown, WhatsApp link works
- **API Response**: Should include `needsUpgrade: true` and WhatsApp contact

### 4. Membership Status API
**Test Case**: Verify membership status endpoint accuracy
- **Steps**:
  1. Call `GET /api/v1/candidate-membership/:cand_id` for various candidates
  2. Test with candidates having 0, 1 Visumes
  3. Verify response structure and data accuracy
- **Expected Response Structure**:
  ```json
  {
    "message": "Membership status retrieved successfully",
    "data": {
      "candidateId": "string",
      "canCreateVisume": boolean,
      "currentVisumeCount": number,
      "allowedVisumes": number,
      "planName": "string",
      "hasActivePlan": boolean,
      "needsUpgrade": boolean,
      "whatsappContact": "string"
    }
  }
  ```

### 5. Dashboard Display
**Test Case**: Verify dashboard shows correct membership information
- **Steps**:
  1. Login as candidates with different Visume counts (0, 1)
  2. Check dashboard membership card display
  3. Verify progress bar accuracy
  4. Test upgrade button functionality
- **Expected Result**: Accurate usage display, working upgrade button

### 6. Error Handling
**Test Case**: System behavior during API failures
- **Steps**:
  1. Test with invalid candidate IDs
  2. Test with network failures
  3. Test with database connection issues
- **Expected Result**: Graceful error handling, fail-safe behavior

## 🔍 Edge Cases to Test

### 1. Concurrent Visume Creation
- **Scenario**: Multiple simultaneous creation attempts
- **Test**: Use multiple browser tabs/sessions
- **Expected**: Only one creation succeeds, others blocked

### 2. Database Inconsistency
- **Scenario**: Candidate has Visumes but no plan record
- **Test**: Manually remove plan record, test behavior
- **Expected**: System handles gracefully, creates default plan

### 3. Plan Expiration
- **Scenario**: Candidate plan has expired
- **Test**: Set plan end_date to past date
- **Expected**: System treats as no active plan

### 4. Invalid WhatsApp Number
- **Scenario**: WhatsApp placeholder not replaced
- **Test**: Check modal with placeholder number
- **Expected**: Clear indication to admin to update number

## 🚀 Manual Testing Checklist

### Backend Testing
- [ ] New candidate registration creates default plan
- [ ] Video profile creation checks membership status
- [ ] Membership API returns correct data
- [ ] Error responses include proper status codes
- [ ] Database transactions are atomic

### Frontend Testing
- [ ] Dashboard shows membership status correctly
- [ ] Progress bar reflects actual usage
- [ ] Limit modal appears when appropriate
- [ ] WhatsApp contact button works
- [ ] Loading states display properly
- [ ] Error states handled gracefully

### Integration Testing
- [ ] End-to-end Visume creation flow
- [ ] Dashboard updates after Visume creation
- [ ] Modal closes properly after contact
- [ ] Responsive design works on mobile
- [ ] Dark mode compatibility

## 🐛 Known Issues & Limitations

### Current Limitations
1. **WhatsApp Number Placeholder**: Needs to be replaced with actual number
2. **Plan Creation**: Creates new plan if not exists (could be optimized)
3. **No Admin Interface**: Plan management requires direct database access

### Future Enhancements (Phase 2 & 3)
1. Credit-based system for employers
2. Admin dashboard for plan management
3. Payment integration
4. Plan upgrade workflows

## 📊 Success Criteria

### Phase 1 Complete When:
- [ ] All test scenarios pass
- [ ] No critical bugs identified
- [ ] User experience is smooth and intuitive
- [ ] Error handling is robust
- [ ] Performance is acceptable
- [ ] Code is properly documented
- [ ] WhatsApp integration works correctly

## 🔧 Testing Tools & Commands

### Database Queries for Verification
```sql
-- Check candidate plans
SELECT c.cand_name, jp.credits, p.plan_name, jp.start_date, jp.end_date
FROM jobseeker c
JOIN jobseekerplans jp ON c.id = jp.cand_id
JOIN plans p ON jp.plan_id = p.id
WHERE c.cand_id = 'CANDIDATE_ID';

-- Check Visume count
SELECT COUNT(*) as visume_count 
FROM videoprofile 
WHERE cand_id = (SELECT id FROM jobseeker WHERE cand_id = 'CANDIDATE_ID');
```

### API Testing with curl
```bash
# Test membership status
curl -X GET "http://localhost:3000/api/v1/candidate-membership/CANDIDATE_ID"

# Test Visume creation (should fail if limit reached)
curl -X POST "http://localhost:3000/api/v1/create-video-resume" \
  -H "Content-Type: application/json" \
  -d '{"candId":"CANDIDATE_ID","jobRole":"Developer","skills":["JavaScript"]}'
```

## 📝 Test Results Documentation

### Test Execution Log
- **Date**: [TO BE FILLED]
- **Tester**: [TO BE FILLED]
- **Environment**: [Development/Staging]
- **Results**: [PASS/FAIL with details]

### Issues Found
- **Issue ID**: [Unique identifier]
- **Severity**: [Critical/High/Medium/Low]
- **Description**: [Detailed description]
- **Steps to Reproduce**: [Clear steps]
- **Expected vs Actual**: [What should happen vs what happens]
- **Status**: [Open/In Progress/Resolved]
