import { useEffect, useState } from "react";
import { Save, X, Check } from "lucide-react";
import { HiAdjustments } from "react-icons/hi";
import { useNavigate } from "react-router-dom";
import Cookies from "js-cookie";
import toast from "react-hot-toast";

const GeneralProfile = () => {
  const navigate = useNavigate();
  const emp_id = Cookies.get("employerId");

  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [editActive, setEditActive] = useState(false);
  const [empName, setEmpName] = useState("");
  const [empEmail, setEmpEmail] = useState("");
  const [empMobile, setEmpMobile] = useState("");
  const [designation, setDesignation] = useState("");
  const [companyName, setCompanyName] = useState("");

  const [profilePicture, setProfilePicture] = useState(null);
  const [includeInReports, setIncludeInReports] = useState(true);
  const [includeInEmails, setIncludeInEmails] = useState(true);

  // Store original values for cancel functionality
  const [originalValues, setOriginalValues] = useState({
    empName: "",
    empEmail: "",
    empMobile: "",
    designation: "",
    companyName: "",
  });

  // Add state for profile picture file
  const [profilePictureFile, setProfilePictureFile] = useState(null);

  // Handle profile picture upload
  const handleProfilePictureUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      // Validate file type and size
      const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
      const maxSize = 5 * 1024 * 1024; // 5MB

      if (!validTypes.includes(file.type)) {
        toast.error("Please upload a valid image file (JPG, PNG, GIF)");
        return;
      }

      if (file.size > maxSize) {
        toast.error("File size must be less than 5MB");
        return;
      }

      // Create preview URL
      const previewUrl = URL.createObjectURL(file);
      setProfilePicture(previewUrl);

      // Store the file for upload
      setProfilePictureFile(file);
      toast.success("Profile picture selected successfully");
    }
  };



  // Save profile changes
  const handleSave = async () => {
    if (!empName.trim()) {
      toast.error("Full name is required");
      return;
    }

    if (!empEmail.trim()) {
      toast.error("Email is required");
      return;
    }

    if (!empMobile.trim()) {
      toast.error("Mobile number is required");
      return;
    }

    if (!designation.trim()) {
      toast.error("Designation is required");
      return;
    }

    setSaving(true);
    try {
      const formData = new FormData();
      formData.append("emp_name", empName.trim());
      formData.append("emp_email", empEmail.trim());
      formData.append("emp_mobile", empMobile.trim());
      formData.append("designation", designation.trim());
      formData.append("company_name", (companyName || "").trim());

      // Add profile picture if selected
      if (profilePictureFile) {
        formData.append("profile_picture", profilePictureFile);
      }

      const response = await fetch(
        `${import.meta.env.VITE_APP_HOST}/api/v1/updateEmployerProfile`,
        {
          method: "PUT",
          headers: {
            Authorization: emp_id,
          },
          body: formData,
        }
      );

      const result = await response.json();

      if (response.ok) {
        toast.success("Profile updated successfully!");

        // If profile picture was uploaded, update the display immediately
        if (result.data?.profile_picture) {
          const fullImagePath = `${import.meta.env.VITE_APP_HOST}/${result.data.profile_picture}`;
          setProfilePicture(fullImagePath);

          console.log('🔄 Profile picture updated, dispatching event:', {
            profile_picture: result.data.profile_picture,
            fullImagePath: fullImagePath
          });

          // Store update flag in localStorage for components that are not currently mounted
          localStorage.setItem('profilePictureUpdated', JSON.stringify({
            timestamp: Date.now(),
            profile_picture: result.data.profile_picture,
            fullImagePath: fullImagePath
          }));

          // Dispatch custom event to notify currently mounted components
          // Add a small delay to ensure all components have registered their listeners
          setTimeout(() => {
            const profilePictureUpdatedEvent = new CustomEvent('profilePictureUpdated', {
              detail: {
                profile_picture: result.data.profile_picture,
                fullImagePath: fullImagePath
              }
            });
            window.dispatchEvent(profilePictureUpdatedEvent);
            console.log('✅ profilePictureUpdated event dispatched successfully');
          }, 100);

        }

        // Clear the file input
        setProfilePictureFile(null);

        setEditActive(false);
        // Update original values
        setOriginalValues({
          empName,
          empEmail,
          empMobile,
          designation,
          companyName,
        });
        // Refresh profile data to ensure consistency
        await getProfileData();
      } else {
        toast.error(result.message || "Failed to update profile");
      }
    } catch (error) {
      console.error("Error updating profile:", error);
      toast.error("Failed to update profile. Please try again.");
    } finally {
      setSaving(false);
    }
  };

  // Cancel editing and revert changes
  const handleCancel = () => {
    setEmpName(originalValues.empName);
    setEmpEmail(originalValues.empEmail);
    setEmpMobile(originalValues.empMobile);
    setDesignation(originalValues.designation);
    setCompanyName(originalValues.companyName);
    setProfilePictureFile(null);
    // Reset profile picture to original if it was changed
    getProfileData();
    setEditActive(false);
    toast.success("Changes cancelled");
  };

  // Start editing mode
  const handleEdit = () => {
    setOriginalValues({
      empName,
      empEmail,
      empMobile,
      designation,
    });
    setEditActive(true);
  };

  const getProfileData = async () => {
    setLoading(true);
    try {
      const profileReq = await fetch(
        `${import.meta.env.VITE_APP_HOST}/api/v1/getEmployerDetails`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            Authorization: emp_id,
          },
        }
      );
      const profileJson = await profileReq.json();
      
      const profileData = profileJson.data || {};
      console.log(profileData);
      if (profileData) {
        setEmpName(profileData.emp_name || "");
        setEmpEmail(profileData.emp_email || "");
        setEmpMobile(profileData.emp_mobile ? profileData.emp_mobile.toString() : "");
        setDesignation(profileData.designation || "");
        setCompanyName(profileData.company_name || "");

        // Set profile picture if available
        if (profileData.profile_picture) {
          const profilePicUrl = profileData.profile_picture.startsWith('http')
            ? profileData.profile_picture
            : `${import.meta.env.VITE_APP_HOST}/${profileData.profile_picture}`;
          setProfilePicture(profilePicUrl);
        }

        // Set original values
        setOriginalValues({
          empName: profileData.emp_name || "",
          empEmail: profileData.emp_email || "",
          empMobile: profileData.emp_mobile ? profileData.emp_mobile.toString() : "",
          designation: profileData.designation || "",
          companyName: profileData.company_name || "",
        });
      }
    } catch (err) {
      console.error("Error fetching profile data:", err);
      toast.error("Failed to load profile data. Please refresh the page.");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (emp_id) {
      getProfileData();
    } else {
      navigate("/candidate/signIn");
    }

    // Add a test button to manually trigger the event (for debugging)
    const testEvent = () => {
      console.log('🧪 Manual test: Dispatching profilePictureUpdated event');
      const testProfilePictureUpdatedEvent = new CustomEvent('profilePictureUpdated', {
        detail: {
          profile_picture: 'test-image.jpg',
          fullImagePath: 'http://localhost:8000/test-image.jpg'
        }
      });
      window.dispatchEvent(testProfilePictureUpdatedEvent);
    };

    // Make the test function available globally for debugging
    window.testProfilePictureEvent = testEvent;

  }, []);

  return (
    <div className="p-6">
      {/* Header Section */}
      <div className="mb-8 flex items-center gap-3">
        <div className="flex items-center justify-center w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg">
          <HiAdjustments className="w-4 h-4 text-white" />
        </div>
        <h2 className="text-xl font-bold text-gray-900 dark:text-white">General Settings</h2>
      </div>
      {loading ? (
        <div className="flex items-center justify-center py-16">
          <div className="flex items-center space-x-3">
            <div className="h-6 w-6 animate-spin rounded-full border-2 border-blue-500 border-t-transparent"></div>
            <span className="text-sm text-gray-600 dark:text-gray-400">Loading profile...</span>
          </div>
        </div>
      ) : (
        <div className="space-y-6">
          {/* Employer Profile Card */}
          <div className="bg-gradient-to-br from-gray-50 to-gray-100 dark:from-navy-700 dark:to-navy-800 rounded-xl border border-gray-200 dark:border-navy-600 p-6 shadow-sm">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-4">
                {/* Display profile picture if available, otherwise show initials - clickable for upload */}
                <div
                  className={`relative ${editActive ? 'cursor-pointer' : ''}`}
                  onClick={() => editActive && document.getElementById('profile-picture-upload').click()}
                >
                  {profilePicture ? (
                    <div className="h-16 w-16 rounded-full overflow-hidden border border-gray-200 dark:border-gray-600 shadow-lg">
                      <img
                        src={profilePicture}
                        alt="Profile Picture"
                        className="h-full w-full object-cover"
                      />
                      {editActive && (
                        <div className="absolute inset-0 bg-black bg-opacity-50 rounded-full flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity duration-200">
                          <span className="text-white text-xs font-medium">Change</span>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="flex h-16 w-16 items-center justify-center rounded-full bg-gradient-to-br from-blue-500 to-indigo-600 text-white shadow-lg">
                      <span className="text-lg font-bold">
                        {empName ? empName.slice(0, 2).toUpperCase() : ""}
                      </span>
                      {editActive && (
                        <div className="absolute inset-0 bg-black bg-opacity-30 rounded-full flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity duration-200">
                          <span className="text-white text-xs font-medium">Upload</span>
                        </div>
                      )}
                    </div>
                  )}
                  {/* Hidden file input */}
                  <input
                    id="profile-picture-upload"
                    type="file"
                    accept="image/*"
                    onChange={handleProfilePictureUpload}
                    className="hidden"
                    disabled={!editActive}
                  />
                </div>
                <div>
                  <h1 className="text-xl font-bold text-gray-900 dark:text-white">{empName}</h1>
                  <p className="text-gray-600 dark:text-gray-400 text-sm">
                    {companyName ? `${designation} • ${companyName}` : designation}
                  </p>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex items-center gap-3">
                {editActive ? (
                  <>
                    <button
                      onClick={handleCancel}
                      disabled={saving}
                      className="px-4 py-2 bg-gray-500 hover:bg-gray-600 disabled:bg-gray-400 text-white rounded-lg font-medium transition-all duration-200 shadow-sm hover:shadow-md flex items-center gap-2"
                    >
                      <X className="w-4 h-4" />
                      Cancel
                    </button>
                    {/* Fix: Change the save button color to match the blue gradient */}
                    <button
                      onClick={handleSave}
                      disabled={saving}
                      className="px-4 py-2 bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 disabled:from-blue-400 disabled:to-indigo-400 text-white rounded-lg font-medium transition-all duration-200 shadow-sm hover:shadow-md flex items-center gap-2"
                    >
                      {saving ? (
                        <>
                          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                          Saving...
                        </>
                      ) : (
                        <>
                          <Save className="w-4 h-4" />
                          Save Changes
                        </>
                      )}
                    </button>
                  </>
                ) : (
                  <button
                    onClick={handleEdit}
                    className="px-4 py-2 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-lg font-medium hover:from-blue-600 hover:to-indigo-700 transition-all duration-200 shadow-sm hover:shadow-md"
                  >
                    Edit Profile
                  </button>
                )}
              </div>
            </div>

            <div className="border-t border-gray-200 dark:border-navy-600 pt-6">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Personal Information</h2>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-6">
                Update your personal details and contact information.
              </p>

              {/* Personal Details Section */}
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    Contact Details
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                    This information will be used for your employer profile.
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Full Name
                    </label>
                    <input
                      type="text"
                      value={empName}
                      onChange={(e) => setEmpName(e.target.value)}
                      className={`w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-200 ${
                        !editActive ? "bg-gray-50 dark:bg-gray-800 cursor-not-allowed" : ""
                      }`}
                      placeholder="Enter your full name"
                      disabled={!editActive}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Email Address
                    </label>
                    <input
                      type="email"
                      value={empEmail}
                      onChange={(e) => setEmpEmail(e.target.value)}
                      className={`w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-200 ${
                        !editActive ? "bg-gray-50 dark:bg-gray-800 cursor-not-allowed" : ""
                      }`}
                      placeholder="Enter your email address"
                      disabled={!editActive}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Mobile Number
                    </label>
                    <input
                      type="tel"
                      value={empMobile}
                      onChange={(e) => setEmpMobile(e.target.value)}
                      className={`w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-200 ${
                        !editActive ? "bg-gray-50 dark:bg-gray-800 cursor-not-allowed" : ""
                      }`}
                      placeholder="Enter your mobile number"
                      disabled={!editActive}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Designation
                    </label>
                    <input
                      type="text"
                      value={designation}
                      onChange={(e) => setDesignation(e.target.value)}
                      className={`w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-200 ${
                        !editActive ? "bg-gray-50 dark:bg-gray-800 cursor-not-allowed" : ""
                      }`}
                      placeholder="Enter your designation"
                      disabled={!editActive}
                    />
                  </div>

                  {/* Company Name Field */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Company Name <span className="text-gray-500 text-xs">(Optional)</span>
                    </label>
                    <input
                      type="text"
                      value={companyName}
                      onChange={(e) => setCompanyName(e.target.value)}
                      className={`w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-200 ${
                        !editActive ? "bg-gray-50 dark:bg-gray-800 cursor-not-allowed" : ""
                      }`}
                      placeholder="Enter your company name (optional)"
                      disabled={!editActive}
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>


        </div>
      )}
    </div>
  );
};

export default GeneralProfile;