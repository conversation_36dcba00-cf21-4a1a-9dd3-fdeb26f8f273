// Employer Profile Management Functions

const prisma = require("../../config/prisma");
const { convertBigIntToNumber } = require("../../utils/bigintHelper");

// Fetch profiles by employer ID
exports.getProfilesByEmployerId = async (req, res) => {
  const { emp_id } = req.params;

  try {
    const profiles = await prisma.employerprofiles.findMany({
      where: { emp_id: Number(emp_id) },
    });

    if (!profiles || profiles.length === 0) {
      return res
        .status(404)
        .json({ message: "No profiles found for this employer." });
    }

    res.status(200).json({
      message: "Profiles fetched successfully.",
      data: convertBigIntToNumber(profiles),
    });
  } catch (error) {
    console.error("Error fetching profiles:", error);
    res.status(500).send("Failed to fetch profiles.");
  }
};

// Get employer profile plan and candidate counts
exports.getEmployerProfilesData = async (req, res) => {
  const emp_id = req.headers["authorization"];

  if (!emp_id) {
    return res
      .status(400)
      .json({ message: "Unauthorized Access, Please Log in Again." });
  }

  try {
    const employerPlan = await prisma.employerplans.findFirst({
      where: { emp_id: Number(emp_id) },
      select: { plan_id: true, creditsLeft: true, end_date: true },
    });

    if (!employerPlan) {
      return res.status(404).json({
        message: "No employer profile plan found. Please log in again.",
      });
    }

    const planDetails = await prisma.plans.findUnique({
      where: { id: employerPlan.plan_id },
    });

    if (!planDetails) {
      return res.status(404).json({
        message: "No plan details found for the employer profile.",
      });
    }

    const employer = await prisma.employer.findUnique({
      where: { id: Number(emp_id) },
      select: { emp_name: true, profile_picture: true, company_id: true },
    });

    if (!employer) {
      return res.status(404).json({
        message: "Employer not found. Please log in again.",
      });
    }

    // Fetch company data if company_id exists
    let company = null;
    if (employer.company_id !== null && employer.company_id !== undefined) {
      company = await prisma.company.findUnique({
        where: { id: employer.company_id },
        select: { company_logo: true, company_name: true, company_website: true },
      });
    }

    const shortlisted_count = await prisma.employerprofiles.count({
      where: { emp_id: Number(emp_id), status: "shortlisted" },
    });

    const unlocked_count = await prisma.employerprofiles.count({
      where: { emp_id: Number(emp_id), status: "unlocked" },
    });

    res.status(200).json({
      message: "Employer profile plan fetched successfully.",
      data: convertBigIntToNumber({
        ...planDetails,
        emp_name: employer.emp_name,
        profile_picture: employer.profile_picture,
        creditsLeft: employerPlan.creditsLeft || 0,
        end_date: employerPlan.end_date || 0,
        candidate_counts: {
          shortlisted_count,
          unlocked_count,
        },
        // Include company data if available
        ...(company && {
          company_logo: company.company_logo,
          company_name: company.company_name,
          company_website: company.company_website,
        }),
      }),
    });
  } catch (error) {
    console.error("Error fetching profiles:", error);
    return res.status(500).json({ message: "Failed to fetch profiles." });
  }
};

// Update employer profile
exports.updateEmployerProfile = async (req, res) => {
  const emp_id = req.headers.authorization;
  const { emp_name, emp_email, emp_mobile, designation, company_name } = req.body;

  console.log("=== UPDATE EMPLOYER PROFILE DEBUG ===");
  console.log("emp_id:", emp_id);
  console.log("Request body:", req.body);
  console.log("Request files:", req.files);
  console.log("=====================================");

  if (!emp_id) {
    console.error("Missing authorization header");
    return res.status(401).json({
      message: "Authorization header (employer ID) is required.",
    });
  }

  try {
    // Validate input (company_name is optional)
    if (!emp_name || !emp_email || !emp_mobile || !designation) {
      console.error("Missing required fields:", { emp_name, emp_email, emp_mobile, designation });
      return res.status(400).json({
        message: "Full name, email, mobile number, and designation are required.",
      });
    }

    // Check if employer exists
    console.log("Checking if employer exists with ID:", Number(emp_id));
    const employer = await prisma.employer.findUnique({
      where: { id: Number(emp_id) },
    });

    if (!employer) {
      console.error("Employer not found with ID:", Number(emp_id));
      return res.status(404).json({
        message: "Employer not found.",
      });
    }

    console.log("Found employer:", employer);

    // Prepare update data for employer
    console.log("Preparing update data...");
    const employerUpdateData = {
      emp_name: emp_name.trim(),
      emp_email: emp_email.trim(),
      emp_mobile: BigInt(emp_mobile),
      designation: designation.trim(),
      company_name: company_name ? company_name.trim() : null,
    };

    // Handle profile picture upload if provided
    if (req.files?.profile_picture) {
      console.log("Processing profile picture upload...");
      const profilePicFile = req.files.profile_picture[0];
      const profilePicPath = profilePicFile.path.replace(/\\/g, "/");
      employerUpdateData.profile_picture = profilePicPath;
      console.log("Profile picture path:", profilePicPath);
    }

    console.log("Final update data:", employerUpdateData);

    // Update employer record
    console.log("Updating employer record...");
    await prisma.employer.update({
      where: { id: Number(emp_id) },
      data: employerUpdateData,
    });

    console.log("Employer profile updated successfully");

    res.status(200).json({
      message: "Employer profile updated successfully.",
      data: {
        emp_name: emp_name.trim(),
        emp_email: emp_email.trim(),
        emp_mobile: emp_mobile,
        designation: designation.trim(),
        company_name: company_name ? company_name.trim() : null,
        ...(req.files?.profile_picture && { profile_picture: req.files.profile_picture[0].path.replace(/\\/g, "/") }),
      },
    });
  } catch (error) {
    console.error("=== ERROR UPDATING EMPLOYER PROFILE ===");
    console.error("Error message:", error.message);
    console.error("Error stack:", error.stack);
    console.error("Error code:", error.code);
    console.error("Error details:", error);
    console.error("=======================================");

    return res.status(500).json({
      message: "Failed to update employer profile.",
      error: error.message,
      details: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
};

// Get employer and company details
exports.getEmployerDetails = async (req, res) => {
  const emp_id = req.headers["authorization"];

  if (!emp_id) {
    return res
      .status(400)
      .json({ message: "Unauthorized Access, Please Log in Again." });
  }

  try {
    const employer = await prisma.employer.findUnique({
      where: { id: Number(emp_id) },
    });

    if (!employer) {
      return res.status(404).json({
        message: "No employer details Found. Please log in again.",
      });
    }

    // Fetch company if company_id exists
    let company = null;
    if (employer.company_id !== null && employer.company_id !== undefined) {
      company = await prisma.company.findUnique({
        where: { id: employer.company_id },
      });
    }

    // Always return employer data, with company data if available
    const responseData = {
      ...employer,
      ...(company && company), // Merge company data if it exists
    };

    res.status(200).json({
      message: "Employer profile data fetched successfully.",
      data: convertBigIntToNumber(responseData),
    });
  } catch (error) {
    console.error("Error fetching Employer Data:", error);
    return res.status(500).json({ message: "Failed to fetch Employer Data." });
  }
};