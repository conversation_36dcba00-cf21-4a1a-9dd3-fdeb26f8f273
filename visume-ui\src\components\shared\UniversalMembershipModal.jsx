import React from "react";
import { X, MessageCircle, Crown, Sparkles, CreditCard } from "lucide-react";

const UniversalMembershipModal = ({ 
  isOpen, 
  onClose, 
  userType = 'candidate', // 'candidate' or 'employer'
  membershipStatus = {}, 
  whatsappNumber = "[WHATSAPP_NUMBER_PLACEHOLDER]" 
}) => {
  if (!isOpen) return null;

  // Dynamic content based on user type
  const getContent = () => {
    if (userType === 'employer') {
      return {
        title: "Credit Limit Reached",
        subtitle: "You've used all your free credits",
        usageLabel: "Credits Used",
        usageText: `${membershipStatus.currentCredits || 0} credits remaining`,
        limitMessage: `You have used all your ${membershipStatus.totalCredits || 10} free credits`,
        actionMessage: "To unlock more candidate profiles and access additional features, please contact us to upgrade your plan.",
        whatsappMessage: "Hi! I've used all my free credits and would like to upgrade my employer plan to unlock more candidate profiles. Can you help me with the available options?",
        icon: CreditCard,
        gradientFrom: "from-blue-600",
        gradientTo: "to-cyan-600"
      };
    } else {
      return {
        title: "Upgrade Required",
        subtitle: "You've reached your free Visume limit",
        usageLabel: "Visumes Used",
        usageText: `${membershipStatus.currentVisumeCount || 0} / ${membershipStatus.allowedVisumes || 1}`,
        limitMessage: `You have used your ${membershipStatus.allowedVisumes || 1} free Visume${membershipStatus.allowedVisumes !== 1 ? 's' : ''}`,
        actionMessage: "To create more Visumes and unlock additional features, please contact us to upgrade your plan.",
        whatsappMessage: "Hi! I've used my free Visume and would like to upgrade my plan to create more video resumes. Can you help me with the available options?",
        icon: Crown,
        gradientFrom: "from-purple-600",
        gradientTo: "to-blue-600"
      };
    }
  };

  const content = getContent();
  const IconComponent = content.icon;

  const handleWhatsAppContact = () => {
    const message = encodeURIComponent(content.whatsappMessage);
    const whatsappUrl = `https://wa.me/${whatsappNumber.replace(/[^\d]/g, '')}?text=${message}`;
    window.open(whatsappUrl, '_blank');
  };

  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div 
      className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 backdrop-blur-sm"
      onClick={handleBackdropClick}
    >
      <div className="relative w-full max-w-md mx-4 bg-white dark:bg-gray-800 rounded-2xl shadow-2xl overflow-hidden">
        {/* Header with gradient background */}
        <div className={`relative bg-gradient-to-br ${content.gradientFrom} via-blue-600 ${content.gradientTo} p-6 text-white`}>
          <button
            onClick={onClose}
            className="absolute top-4 right-4 p-2 rounded-full bg-white/20 hover:bg-white/30 transition-colors duration-200"
          >
            <X className="w-5 h-5" />
          </button>
          
          <div className="flex items-center gap-3 mb-2">
            <div className="p-2 bg-white/20 rounded-full">
              <IconComponent className="w-6 h-6" />
            </div>
            <h2 className="text-xl font-bold">{content.title}</h2>
          </div>
          
          <p className="text-white/90 text-sm">
            {content.subtitle}
          </p>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Status Information */}
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-xl p-4 mb-6">
            <div className="flex items-center justify-between mb-3">
              <span className="text-sm font-medium text-gray-600 dark:text-gray-300">
                Current Plan
              </span>
              <span className="text-sm font-semibold text-blue-600 dark:text-blue-400">
                {membershipStatus.planName || (userType === 'employer' ? 'Free Employer Plan' : 'Free Plan')}
              </span>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-600 dark:text-gray-300">
                {content.usageLabel}
              </span>
              <span className="text-sm font-semibold text-purple-600 dark:text-purple-400">
                {content.usageText}
              </span>
            </div>
          </div>

          {/* Main Message */}
          <div className="text-center mb-6">
            <div className="w-16 h-16 bg-gradient-to-br from-orange-100 to-red-100 dark:from-orange-900/50 dark:to-red-900/50 rounded-full flex items-center justify-center mx-auto mb-4">
              <Sparkles className="w-8 h-8 text-orange-600 dark:text-orange-400" />
            </div>
            
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              {content.limitMessage}
            </h3>
            
            <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">
              {content.actionMessage}
            </p>
          </div>

          {/* Action Buttons */}
          <div className="space-y-3">
            <button
              onClick={handleWhatsAppContact}
              className="w-full flex items-center justify-center gap-3 bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white px-6 py-3 rounded-xl font-medium transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
            >
              <MessageCircle className="w-5 h-5" />
              Contact us on WhatsApp
            </button>
            
            <button
              onClick={onClose}
              className="w-full bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 px-6 py-3 rounded-xl font-medium transition-colors duration-200"
            >
              Maybe Later
            </button>
          </div>

          {/* Additional Info */}
          <div className="mt-6 p-4 bg-gray-50 dark:bg-gray-700/50 rounded-xl">
            <p className="text-xs text-gray-500 dark:text-gray-400 text-center">
              Our team will help you choose the perfect plan for your needs and unlock {userType === 'employer' ? 'unlimited profile access' : 'unlimited Visume creation'}.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UniversalMembershipModal;
