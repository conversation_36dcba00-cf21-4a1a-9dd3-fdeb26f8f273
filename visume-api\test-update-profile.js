const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');

async function testUpdateProfile() {
  try {
    const formData = new FormData();
    formData.append('emp_name', 'Test User Updated');
    formData.append('emp_email', '<EMAIL>');
    formData.append('emp_mobile', '1234567890');
    formData.append('designation', 'Software Engineer');
    formData.append('company_name', 'Test Company Inc');

    console.log('Testing API endpoint...');
    const response = await axios.put('http://localhost:8000/api/v1/updateEmployerProfile', formData, {
      headers: {
        ...formData.getHeaders(),
        'Authorization': '1' // Using employer ID 1 for testing
      }
    });

    console.log('Success:', response.data);
  } catch (error) {
    console.error('Error:', error.response?.data || error.message);
    console.error('Status:', error.response?.status);
    if (error.response?.status === 500) {
      console.error('Server Error Details:', error.response.data);
    }
  }
}

// Test the API
testUpdateProfile();
