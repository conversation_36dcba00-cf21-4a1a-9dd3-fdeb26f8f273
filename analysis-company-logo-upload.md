# Company Logo Upload Implementation Analysis

## Overview
This document analyzes the current working company logo upload implementation to understand the pattern that should be applied to fix the profile picture upload functionality.

## Backend Implementation

### API Route Configuration
**File:** `visume-api/routes/authRoutes.js`
- **Route:** `PUT /api/v1/updateEmployerProfile`
- **Multer Configuration:** Accepts `company_logo` field with maxCount: 1
- **File Storage:** Uses multer diskStorage with destination `utils/files/profile_pics/`
- **File Naming:** `${Date.now()}_${file.fieldname}${ext}`

```javascript
router.put(
  "/updateEmployerProfile",
  upload.fields([{ name: "company_logo", maxCount: 1 }]),
  employerProfileManager.updateEmployerProfile
);
```

### Controller Implementation
**File:** `visume-api/controllers/employer/employerProfileManager.js`
- **Function:** `updateEmployerProfile`
- **File Handling:** Checks `req.files?.company_logo`
- **Path Processing:** Normalizes path with `logoFile.path.replace(/\\/g, "/")`
- **Database Update:** Updates `company.company_logo` field via Prisma

```javascript
// Handle company logo upload if provided
if (req.files?.company_logo) {
  const logoFile = req.files.company_logo[0];
  const logoPath = logoFile.path.replace(/\\/g, "/");
  companyUpdateData.company_logo = logoPath;
}
```

### Database Schema
**File:** `visume-api/prisma/schema.prisma`
- **Table:** `company`
- **Field:** `company_logo String? @db.VarChar(255)`

## Frontend Implementation

### Current Working Pattern (Company Logo)
**File:** `visume-ui/src/views/employer/settings/EmployeeSettings.jsx`
- **Function:** `handleLogoUpload`
- **Validation:** Image type check and 2MB size limit
- **UI Update:** Uses `URL.createObjectURL()` for immediate preview
- **State Management:** Updates `companyData.logo`

```javascript
const handleLogoUpload = (e) => {
  const file = e.target.files[0];
  if (file) {
    if (file.type.startsWith('image/') && file.size <= 2 * 1024 * 1024) {
      const imageUrl = URL.createObjectURL(file);
      setCompanyData(prev => ({ ...prev, logo: imageUrl }));
      toast.success("Company logo updated!");
    } else {
      toast.error("Please upload a valid image file under 2MB.");
    }
  }
};
```

### Profile Picture Upload (Current Issue)
**File:** `visume-ui/src/views/employer/settings/GeneralProfile.jsx`
- **Function:** `handleProfilePictureUpload`
- **API Call:** Uses FormData with `profile_picture` field name
- **Issue:** Backend route doesn't accept `profile_picture` field

```javascript
// Add profile picture if selected
if (profilePictureFile) {
  formData.append("profile_picture", profilePictureFile);
}
```

## Event System for Real-time Updates

### Company Logo Event System (Working)
- **Event Name:** `companyLogoUpdated`
- **Listeners:** EmployerDashboard, CustomNavbar, Navbar components
- **Trigger:** After successful company logo update

### Profile Picture Event System (Implemented)
- **Event Name:** `profilePictureUpdated`
- **Listeners:** EmployerDashboard, CustomNavbar, Navbar components
- **Trigger:** After successful profile picture update
- **Additional:** localStorage flag for cross-component communication

## Key Differences Identified

### 1. Backend Route Configuration
- **Company Logo:** Route accepts `company_logo` field ✅
- **Profile Picture:** Route does NOT accept `profile_picture` field ❌

### 2. Database Storage
- **Company Logo:** Stored in `company.company_logo` field ✅
- **Profile Picture:** Should be stored in `employer.profile_picture` field ❌

### 3. File Field Names
- **Company Logo:** Uses `company_logo` field name ✅
- **Profile Picture:** Uses `profile_picture` field name but route doesn't accept it ❌

## Root Cause Analysis

The profile picture upload is failing because:

1. **Backend Route Issue:** The `updateEmployerProfile` route only accepts `company_logo` field, not `profile_picture`
2. **Controller Logic Missing:** No handling for `profile_picture` field in the controller
3. **Database Update Missing:** No logic to update `employer.profile_picture` field

## Solution Requirements

To fix profile picture upload, we need to:

1. **Update Backend Route:** Add `profile_picture` field to multer configuration
2. **Update Controller:** Add logic to handle `profile_picture` file upload
3. **Update Database Logic:** Add code to update `employer.profile_picture` field
4. **Maintain Event System:** Ensure `profilePictureUpdated` event is dispatched after successful upload

## File Upload Pattern Summary

### Working Company Logo Pattern:
1. Frontend: File selected → FormData with `company_logo` field
2. Backend: Route accepts `company_logo` → Controller processes → Database updated
3. Response: Success → Event dispatched → UI components refresh

### Required Profile Picture Pattern:
1. Frontend: File selected → FormData with `profile_picture` field
2. Backend: Route accepts `profile_picture` → Controller processes → Database updated
3. Response: Success → Event dispatched → UI components refresh
