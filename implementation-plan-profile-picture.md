# Profile Picture Upload Implementation Plan

## Overview
This document outlines the step-by-step changes needed to fix the profile picture upload functionality by applying the exact same pattern used for company logo uploads.

## Root Cause Summary
The profile picture upload is failing because the backend route `/api/v1/updateEmployerProfile` only accepts `company_logo` field but not `profile_picture` field. The frontend is sending `profile_picture` but the backend is not configured to handle it.

## Implementation Steps

### Phase 1: Backend Changes

#### Step 1: Update Route Configuration
**File:** `visume-api/routes/authRoutes.js`
**Line:** ~295
**Change:** Add `profile_picture` field to the multer configuration

**Current:**
```javascript
router.put(
  "/updateEmployerProfile",
  upload.fields([{ name: "company_logo", maxCount: 1 }]),
  employerProfileManager.updateEmployerProfile
);
```

**Updated:**
```javascript
router.put(
  "/updateEmployerProfile",
  upload.fields([
    { name: "company_logo", maxCount: 1 },
    { name: "profile_picture", maxCount: 1 }
  ]),
  employerProfileManager.updateEmployerProfile
);
```

#### Step 2: Update Controller Logic
**File:** `visume-api/controllers/employer/employerProfileManager.js`
**Function:** `updateEmployerProfile`
**Location:** After line 173 (after company logo handling)

**Add Profile Picture Handling:**
```javascript
// Handle profile picture upload if provided
if (req.files?.profile_picture) {
  const profilePictureFile = req.files.profile_picture[0];
  const profilePicturePath = profilePictureFile.path.replace(/\\/g, "/");
  employerUpdateData.profile_picture = profilePicturePath;
}
```

#### Step 3: Update Response Data
**File:** `visume-api/controllers/employer/employerProfileManager.js`
**Location:** Line ~203 (in the response object)

**Current:**
```javascript
res.status(200).json({
  message: "Employer profile updated successfully.",
  data: {
    emp_name: emp_name.trim(),
    company_website: company_website.trim(),
    ...(req.files?.company_logo && { company_logo: req.files.company_logo[0].path.replace(/\\/g, "/") }),
  },
});
```

**Updated:**
```javascript
res.status(200).json({
  message: "Employer profile updated successfully.",
  data: {
    emp_name: emp_name.trim(),
    company_website: company_website.trim(),
    ...(req.files?.company_logo && { company_logo: req.files.company_logo[0].path.replace(/\\/g, "/") }),
    ...(req.files?.profile_picture && { profile_picture: req.files.profile_picture[0].path.replace(/\\/g, "/") }),
  },
});
```

### Phase 2: Frontend Changes

#### Step 4: Verify Frontend Form Submission
**File:** `visume-ui/src/views/employer/settings/GeneralProfile.jsx`
**Status:** ✅ Already correct - sends `profile_picture` field in FormData

The frontend is already correctly configured:
```javascript
// Add profile picture if selected
if (profilePictureFile) {
  formData.append("profile_picture", profilePictureFile);
}
```

#### Step 5: Verify Event Dispatch Logic
**File:** `visume-ui/src/views/employer/settings/GeneralProfile.jsx`
**Status:** ✅ Already implemented - dispatches `profilePictureUpdated` event

The event dispatch logic is already in place:
```javascript
// Store update flag in localStorage for components that are not currently mounted
localStorage.setItem('profilePictureUpdated', JSON.stringify({
  timestamp: Date.now(),
  profile_picture: result.data.profile_picture,
  fullImagePath: fullImagePath
}));

// Dispatch custom event to notify currently mounted components
setTimeout(() => {
  const profilePictureUpdatedEvent = new CustomEvent('profilePictureUpdated', {
    detail: {
      profile_picture: result.data.profile_picture,
      fullImagePath: fullImagePath
    }
  });
  window.dispatchEvent(profilePictureUpdatedEvent);
  console.log('✅ profilePictureUpdated event dispatched successfully');
}, 100);
```

### Phase 3: Database Verification

#### Step 6: Verify Database Schema
**File:** `visume-api/prisma/schema.prisma`
**Status:** ✅ Already correct - `employer` table has `profile_picture` field

The database schema already supports profile pictures:
```prisma
model employer {
  // ... other fields
  profile_picture String? @db.VarChar(255)
  // ... other fields
}
```

### Phase 4: Testing and Validation

#### Step 7: Test Backend Changes
1. Start the backend server
2. Test the API endpoint with a tool like Postman or curl
3. Verify that `profile_picture` files are accepted and processed
4. Check that the database is updated correctly

#### Step 8: Test Frontend Integration
1. Navigate to employer settings page
2. Upload a new profile picture
3. Verify the API call succeeds
4. Check that the event is dispatched
5. Confirm real-time UI updates work

#### Step 9: Test Cross-Component Updates
1. Upload profile picture from settings page
2. Navigate to dashboard home page - verify new image appears
3. Check top navigation dropdown - verify new image appears
4. Test browser refresh - verify image persists

## File Summary

### Files to Modify:
1. `visume-api/routes/authRoutes.js` - Add profile_picture field to multer config
2. `visume-api/controllers/employer/employerProfileManager.js` - Add profile picture handling logic

### Files Already Correct:
1. `visume-ui/src/views/employer/settings/GeneralProfile.jsx` - Frontend form submission ✅
2. `visume-ui/src/views/employer/EmployerDashboard/EmployerDashboard.jsx` - Event listener ✅
3. `visume-ui/src/components/navbar/index.jsx` - Event listener ✅
4. `visume-ui/src/views/employer/components/CustomNavbar.jsx` - Event listener ✅
5. `visume-api/prisma/schema.prisma` - Database schema ✅

## Expected Outcome

After implementing these changes:
1. Profile picture uploads will work correctly
2. The backend will accept and process `profile_picture` files
3. The database will be updated with the new profile picture path
4. The `profilePictureUpdated` event will be dispatched
5. All UI components will update in real-time to show the new profile picture
6. The existing event-driven synchronization system will work as intended

## Risk Assessment

**Low Risk Changes:**
- Adding multer field configuration (non-breaking)
- Adding controller logic (non-breaking)
- Updating response data (non-breaking)

**No Breaking Changes:**
- All existing functionality will continue to work
- Company logo upload will remain unaffected
- Event system is already implemented and tested
