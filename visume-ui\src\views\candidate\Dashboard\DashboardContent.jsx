import React, { useState } from "react";
import defaultProfileImg from "assets/img/default-profile.png";
import JobCard from "../components/JobCard";
import CreateVR from "../components/CreateVR/CreateVR";
import VideoProfileCard from "../components/VideoProfileCard";
import VidProfPopup from "../components/VidProfPopup";
import UpgradeModal from "../components/UpgradeModal";
import { HiOutlineSparkles, HiVideoCamera } from "react-icons/hi";
import SmLoader from "../../../components/SmLoader";
import {
  Eye,
  Video,
  ClipboardCheck,
  MousePointerClick,
  Unlock,
  ArrowUpCircle,
} from "lucide-react";

export default function DashboardContent({
  jstoken,
  candData,
  loadingInfo,
  togglePopupVR,
  profile_picture,
  imageError,
  setImageError,
  userStats,
  jobData,
  videoProfiles,
  isLoading,
  createVRpopup,
  showVideoProfilePopup,
  toggleVideoProfilePopup,
  membershipStatus,
  membershipLoading,
}) {
  // Add state for the upgrade modal
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);

  // Function to toggle the upgrade modal
  const toggleUpgradeModal = () => {
    setShowUpgradeModal(prev => !prev);
  };

  return (
    <>
      {jstoken ? (
        <div className="p-4 space-y-8">
          {/* Streamlined Header with Profile and Stats */}
          <div className="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-800 rounded-xl shadow-sm">
            <div className="flex items-center justify-between p-6">
              {/* Left: Profile Section */}
              <div className="flex items-center gap-4">
                {imageError ? (
                  <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-blue-500 to-purple-600 text-sm font-semibold text-white shadow-lg">
                    {candData && candData?.cand_name[0]?.toUpperCase()}
                  </div>
                ) : (
                  <img
                    className="h-12 w-12 rounded-full border-2 border-gray-200 dark:border-gray-700 object-cover shadow-md"
                    src={profile_picture || defaultProfileImg}
                    alt={candData?.cand_name}
                    onError={(e) => {
                      if (e.target.src !== defaultProfileImg) {
                        e.target.onerror = null;
                        e.target.src = defaultProfileImg;
                      }
                      setImageError(true);
                    }}
                  />
                )}
                <div>
                  <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                    {loadingInfo ? (
                      <div className="w-32 h-5 bg-gray-200 dark:bg-gray-700 animate-pulse rounded" />
                    ) : (
                      candData.cand_name
                    )}
                  </h2>
                  <div className="flex items-center gap-3">
                    {/* Conditional rendering for plan badge or upgrade button */}
                    {membershipStatus && !membershipLoading && !membershipStatus.canCreateVisume ? (
                      <button 
                        onClick={toggleUpgradeModal}
                        className="inline-flex items-center gap-1 rounded-full bg-gradient-to-r from-green-600 to-green-700 px-2.5 py-1 text-xs font-medium text-white hover:from-green-700 hover:to-green-800 transition-all duration-200"
                      >
                        <ArrowUpCircle className="w-3 h-3" />
                        Upgrade Plan
                      </button>
                    ) : (
                      <span className="inline-flex items-center rounded-full bg-gradient-to-r from-blue-500 to-purple-600 px-2.5 py-1 text-xs font-medium text-white">
                        {membershipStatus?.planName || "Free Plan"}
                      </span>
                    )}
                    <div className="flex items-center gap-1 text-xs text-gray-600 dark:text-gray-400">
                      <Video className="w-3 h-3" />
                      <span>
                        {membershipStatus ?
                          `${membershipStatus.currentVisumeCount}/${membershipStatus.allowedVisumes} Visume${membershipStatus.allowedVisumes !== 1 ? 's' : ''}` :
                          '0/1 Visume'
                        }
                      </span>
                      <div className="w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-1 ml-2">
                        <div
                          className="bg-gradient-to-r from-blue-500 to-purple-600 h-1 rounded-full transition-all duration-500"
                          style={{
                            width: membershipStatus ?
                              `${Math.min((membershipStatus.currentVisumeCount / membershipStatus.allowedVisumes) * 100, 100)}%` :
                              "0%"
                          }}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Center: Stats */}
              <div className="hidden lg:flex items-center gap-6">
                {userStats.map(({ count, label, Icon }) => (
                  <div key={label} className="flex items-center gap-2 bg-gray-50 dark:bg-gray-800 rounded-lg px-4 py-3">
                    <div className="p-1.5 bg-white dark:bg-gray-700 rounded-lg">
                      <Icon className="w-4 h-4 text-gray-600 dark:text-gray-400" />
                    </div>
                    <div>
                      <div className="text-xl font-bold text-gray-900 dark:text-white leading-none">
                        {count}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        {label}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              
              {/* Right: Action Button */}
              <button
                onClick={membershipStatus && !membershipStatus.canCreateVisume ? toggleUpgradeModal : togglePopupVR}
                className="inline-flex items-center gap-2 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-5 py-2.5 rounded-lg font-medium transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
              >
                <Video className="w-4 h-4" />
                Create Visume
              </button>
            </div>
            
            {/* Mobile Stats */}
            <div className="lg:hidden border-t border-gray-200 dark:border-gray-800 p-4">
              <div className="grid grid-cols-2 gap-4">
                {userStats.map(({ count, label, Icon }) => (
                  <div key={label} className="flex items-center gap-2 bg-gray-50 dark:bg-gray-800 rounded-lg p-3">
                    <div className="p-1 bg-white dark:bg-gray-700 rounded">
                      <Icon className="w-3 h-3 text-gray-600 dark:text-gray-400" />
                    </div>
                    <div>
                      <div className="text-lg font-bold text-gray-900 dark:text-white leading-none">
                        {count}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        {label}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Enhanced Main Content */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Enhanced Suggested Jobs */}
            <div className="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-800 rounded-xl shadow-sm">
              <div className="p-6 border-b border-gray-200 dark:border-gray-800">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-gradient-to-br from-purple-100 to-pink-100 dark:from-purple-900/50 dark:to-pink-900/50 rounded-lg">
                    <HiOutlineSparkles className="w-5 h-5 text-purple-600 dark:text-purple-400" />
                  </div>
                  <div>
                    <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                      Suggested Jobs
                    </h2>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      AI-curated opportunities for you
                    </p>
                  </div>
                </div>
              </div>
              <div className="p-6">
                {jobData.length > 0 ? (
                  <div className="space-y-3 max-h-96 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600 scrollbar-track-gray-100 dark:scrollbar-track-gray-800">
                    {jobData.map((job) => (
                      <JobCard key={job.id} iconUrl={job.image} job={job} />
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <div className="w-20 h-20 bg-gradient-to-br from-purple-100 to-pink-100 dark:from-purple-900/50 dark:to-pink-900/50 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg">
                      <HiOutlineSparkles className="w-10 h-10 text-purple-600 dark:text-purple-400" />
                    </div>
                    <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                      AI is finding perfect matches
                    </h3>
                    <p className="text-gray-500 dark:text-gray-400 max-w-sm mx-auto">
                      Our intelligent system is analyzing your profile and skills to curate the best job opportunities just for you
                    </p>
                    <div className="mt-6">
                      <div className="flex justify-center">
                        <div className="flex space-x-1">
                          <div className="w-2 h-2 bg-purple-600 rounded-full animate-bounce"></div>
                          <div className="w-2 h-2 bg-purple-600 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                          <div className="w-2 h-2 bg-purple-600 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Enhanced Your Visumes - Right Column */}
            <div className="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-800 rounded-xl shadow-sm">
              <div className="p-6 border-b border-gray-200 dark:border-gray-800">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-gradient-to-br from-blue-100 to-cyan-100 dark:from-blue-900/50 dark:to-cyan-900/50 rounded-lg">
                      <HiVideoCamera className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div>
                      <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                        Your Visumes
                      </h2>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {membershipLoading ? (
                          "Loading membership status..."
                        ) : membershipStatus ? (
                          membershipStatus.statusText
                        ) : (
                          `${videoProfiles.length} video resumes created`
                        )}
                      </p>
                    </div>
                  </div>

                  {/* Membership Status Badge and Upgrade Button */}
                  <div className="flex items-center gap-2">
                    {membershipStatus && !membershipLoading && (
                      <>
                        {membershipStatus.canCreateVisume ? (
                          <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                            membershipStatus.statusColor === 'success'
                              ? 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400'
                              : membershipStatus.statusColor === 'warning'
                              ? 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-400'
                              : membershipStatus.statusColor === 'error'
                              ? 'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400'
                              : 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300'
                          }`}>
                            {membershipStatus.planName}
                          </div>
                        ) : (
                          <button
                            onClick={toggleUpgradeModal}
                            className="px-3 py-1 bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white text-xs font-medium rounded-full transition-all duration-200 shadow-sm hover:shadow-md"
                          >
                            Upgrade
                          </button>
                        )}
                      </>
                    )}
                  </div>
                </div>
              </div>
              <div className="p-6">
                {isLoading ? (
                  <div className="flex items-center justify-center py-12">
                    <SmLoader text={"Loading your video resumes..."} />
                  </div>
                ) : videoProfiles.length === 0 ? (
                  <div className="text-center py-8">
                    <div className="w-16 h-16 bg-gradient-to-br from-blue-100 to-cyan-100 dark:from-blue-900/50 dark:to-cyan-900/50 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
                      <HiVideoCamera className="w-8 h-8 text-blue-600 dark:text-blue-400" />
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                      Ready to stand out?
                    </h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400 mb-4 max-w-sm mx-auto">
                      Create your first video resume and let your personality shine through to potential employers
                    </p>
                    <button
                      onClick={membershipStatus && !membershipStatus.canCreateVisume ? toggleUpgradeModal : togglePopupVR}
                      className="inline-flex items-center gap-2 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-5 py-2 rounded-lg font-medium transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                    >
                      <Video className="w-4 h-4" />
                      Create Your First Visume
                    </button>
                  </div>
                ) : (
                  <div className="space-y-3 max-h-96 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600 scrollbar-track-gray-100 dark:scrollbar-track-gray-800">
                    {videoProfiles.map((profile) => (
                      <VideoProfileCard
                        key={profile.vpid}
                        profile={profile}
                        toggleVideoProfilePopup={toggleVideoProfilePopup}
                      />
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      ) : (
        <h2>Please SignIn</h2>
      )}

      {/* Start Popup for Video Resume */}
      {createVRpopup && <CreateVR key="create-vr" togglePopupVR={togglePopupVR} />}
      {/* End Popup for Video Resume */}

      {/* Video profile popup */}
      {showVideoProfilePopup && (
        <VidProfPopup key="vid-prof" toggleVideoProfilePopup={toggleVideoProfilePopup} />
      )}

      {/* Upgrade Modal */}
      <UpgradeModal 
        isOpen={showUpgradeModal} 
        onClose={toggleUpgradeModal} 
        membershipStatus={membershipStatus || {}}
      />
    </>
  );
}