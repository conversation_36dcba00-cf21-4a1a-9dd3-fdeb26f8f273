const { GoogleGenerativeAI } = require('@google/generative-ai');
const dotenv = require('dotenv');

dotenv.config();

const genAI = new GoogleGenerativeAI(process.env.GOOGLE_API_KEY);
const model = genAI.getGenerativeModel({ model: "gemini-2.0-flash" });

/**
 * IMPROVEMENT: Dynamic Requirement Type Mixing
 *
 * This module has been enhanced to generate requirements in a dynamic, shuffled pattern
 * instead of clustering similar requirement types together. The AI prompt now explicitly
 * instructs the AI to alternate and mix requirement types (technical, behavioral,
 * communication, problem_solving) throughout the interview to create a more engaging
 * and unpredictable interview flow.
 *
 * Key improvements:
 * - AI prompt includes specific instructions for dynamic type mixing
 * - Fallback requirements follow a varied pattern instead of clustering by type
 * - No more than 2 consecutive requirements of the same category
 */

/**
 * Generate personalized interview requirements based on candidate profile
 * @param {Object} candidateProfile - Basic candidate information
 * @param {string} jobRole - Target job role
 * @param {Array} skills - Required skills array
 * @param {string} resumeData - Complete resume data for context
 * @returns {Object} Generated requirements object
 */
exports.generatePersonalizedRequirements = async (candidateProfile, jobRole, skills, resumeData = '') => {
  try {
    const { candId, experience, companyType } = candidateProfile;

    // Generate random number of requirements (7-10) for true variety
    const numRequirements = Math.floor(Math.random() * 4) + 7; // Random number between 7-10
    console.log(`🎲 RANDOM REQUIREMENTS COUNT: ${numRequirements} (ensuring equal distribution across 7-10 range)`);

    const prompt = `You are an expert technical recruiter creating personalized interview requirements.

CANDIDATE PROFILE:
- Role Applied For: ${jobRole}
- Required Skills: ${skills.join(', ')}
- Experience Level: ${experience}
- Company Type: ${companyType}
- Resume/Background: ${resumeData || 'Limited information available'}

Generate exactly ${numRequirements} simple parameter names for evaluation tailored to this candidate's profile.
IMPORTANT: You must generate exactly ${numRequirements} requirements - no more, no less.

Requirements should be:
1. Tailored to candidate's experience level (${experience})
2. Relevant to the role (${jobRole}) and company type (${companyType})
3. Assessable through interview responses
4. Progressive in difficulty
5. Cover technical skills, communication, and problem-solving
6. Provide comprehensive coverage of role expectations

CRITICAL REQUIREMENT DISTRIBUTION INSTRUCTION:
Create a DYNAMIC, SHUFFLED MIX of requirement types throughout the list. DO NOT generate requirements in blocks or clusters by type.
Instead of grouping all technical requirements together followed by all behavioral requirements, you must:
- ALTERNATE and MIX requirement types (technical, behavioral, communication, problem_solving) throughout the entire list
- Create an UNPREDICTABLE, VARIED SEQUENCE that keeps the interview engaging
- Ensure NO MORE than 2 consecutive requirements of the same category
- Example good pattern: technical → behavioral → technical → communication → problem_solving → technical → behavioral
- Example good pattern: technical → technical → behavioral → communication → problem_solving → technical → behavioral
- Example bad pattern: technical → technical → technical → behavioral → behavioral → behavioral

REQUIREMENT CATEGORIES to mix dynamically:
- "technical": Skills, frameworks, coding knowledge, system design
- "behavioral": Teamwork, leadership, adaptability, project ownership
- "communication": Explaining concepts, presentation skills, documentation
- "problem_solving": Analytical thinking, debugging, troubleshooting

Each requirement should be a SIMPLE PARAMETER NAME (2-4 words max), not a full sentence.
Examples: "JavaScript proficiency", "React knowledge", "API integration skills", "Problem solving", "Communication skills"

Return EXACTLY this JSON structure:
{
  "requirements": [
    {
      "id": "req_technical_1",
      "parameter": "JavaScript proficiency",
      "category": "technical",
      "priority": "high",
      "assessmentCriteria": [
        "Provides specific code examples or project details",
        "Explains technical concepts clearly",
        "Shows understanding of best practices"
      ],
      "experienceLevel": "${experience}",
      "satisfied": false,
      "satisfactionScore": 0,
      "evidence": []
    }
  ],
  "assessmentStrategy": {
    "completionThreshold": 0.75,
    "minRequiredSatisfied": 5,
    "focusAreas": ["Technical Skills", "Problem Solving", "Communication"]
  },
  "candidateContext": {
    "experienceLevel": "${experience}",
    "targetRole": "${jobRole}",
    "keySkills": ${JSON.stringify(skills)}
  }
}`;

    const result = await model.generateContent(prompt);
    const response = result.response.text();

    return parseAndValidateRequirements(response, candidateProfile, jobRole, skills);

  } catch (error) {
    console.error('Error generating requirements:', error);
    return createFallbackRequirements(jobRole, skills, candidateProfile.experience, numRequirements);
  }
};

/**
 * Parse and validate AI-generated requirements
 * @param {string} response - Raw AI response
 * @param {Object} candidateProfile - Candidate profile for validation
 * @param {string} jobRole - Job role for fallback
 * @param {Array} skills - Skills array for fallback
 * @returns {Object} Validated requirements object
 */
const parseAndValidateRequirements = (response, candidateProfile, jobRole, skills) => {
  try {
    // Clean the response to extract JSON
    let cleaned = response.trim().replace(/```json\n?/g, '').replace(/```\n?/g, '');
    const parsed = JSON.parse(cleaned);

    // Validate and normalize requirements
    const validatedRequirements = parsed.requirements.map((req, index) => ({
      id: req.id || `req_${index + 1}`,
      parameter: String(req.parameter || req.description || ''), // Support both new 'parameter' and legacy 'description'
      category: req.category || 'technical',
      priority: req.priority || 'medium',
      assessmentCriteria: Array.isArray(req.assessmentCriteria) ? req.assessmentCriteria : [],
      experienceLevel: req.experienceLevel || candidateProfile.experience,
      satisfied: false,
      satisfactionScore: 0,
      evidence: [],
      lastAssessed: null
    }));

    // Phase 3: Calculate dynamic minRequiredSatisfied based on requirement count
    const dynamicMinRequired = Math.max(5, Math.floor(validatedRequirements.length * 0.7));

    return {
      requirements: validatedRequirements,
      assessmentStrategy: {
        completionThreshold: Number(parsed.assessmentStrategy?.completionThreshold) || 0.75,
        minRequiredSatisfied: Number(parsed.assessmentStrategy?.minRequiredSatisfied) || dynamicMinRequired,
        focusAreas: Array.isArray(parsed.assessmentStrategy?.focusAreas) ?
          parsed.assessmentStrategy.focusAreas : ['Technical Skills', 'Communication']
      },
      candidateContext: {
        experienceLevel: parsed.candidateContext?.experienceLevel || candidateProfile.experience,
        targetRole: parsed.candidateContext?.targetRole || jobRole,
        keySkills: Array.isArray(parsed.candidateContext?.keySkills) ?
          parsed.candidateContext.keySkills : skills
      },
      generatedAt: new Date().toISOString(),
      version: '1.0'
    };

  } catch (error) {
    console.error('Error parsing requirements:', error);
    throw new Error(`Failed to parse AI requirements: ${error.message}`);
  }
};

/**
 * Create fallback requirements when AI generation fails
 * @param {string} jobRole - Job role
 * @param {Array} skills - Skills array
 * @param {string} experience - Experience level
 * @param {number} numRequirements - Number of requirements to generate (passed from main function)
 * @returns {Object} Fallback requirements object
 */
const createFallbackRequirements = (jobRole, skills, experience, numRequirements = null) => {
  // Use passed numRequirements or generate random if not provided (for backward compatibility)
  const targetRequirements = numRequirements || (Math.floor(Math.random() * 4) + 7);
  console.log(`🎲 FALLBACK REQUIREMENTS: Generating ${targetRequirements} requirements (${numRequirements ? 'using main function count' : 'random fallback'})`);

  // IMPROVED: Mixed requirement types for dynamic interview flow
  // Pattern: technical → communication → behavioral → technical → problem_solving → technical → behavioral → communication → technical → behavioral
  const baseRequirements = [
    {
      id: "req_technical_1",
      parameter: `${skills[0] || 'Technical'} proficiency`,
      category: "technical",
      priority: "high",
      assessmentCriteria: [
        "Shows practical knowledge of required technologies",
        "Provides specific examples from experience",
        "Explains technical concepts clearly"
      ],
      experienceLevel: experience,
      satisfied: false,
      satisfactionScore: 0,
      evidence: [],
      lastAssessed: null
    },
    {
      id: "req_communication_1",
      parameter: "Communication skills",
      category: "communication",
      priority: "high",
      assessmentCriteria: [
        "Explains ideas clearly and concisely",
        "Uses appropriate technical terminology",
        "Responds effectively to follow-up questions"
      ],
      experienceLevel: experience,
      satisfied: false,
      satisfactionScore: 0,
      evidence: [],
      lastAssessed: null
    },
    {
      id: "req_behavioral_1",
      parameter: "Team collaboration",
      category: "behavioral",
      priority: "high",
      assessmentCriteria: [
        "Describes effective teamwork experiences",
        "Shows ability to work with diverse teams",
        "Demonstrates conflict resolution skills"
      ],
      experienceLevel: experience,
      satisfied: false,
      satisfactionScore: 0,
      evidence: [],
      lastAssessed: null
    },
    {
      id: "req_technical_2",
      parameter: `${skills[1] || 'Framework'} knowledge`,
      category: "technical",
      priority: "high",
      assessmentCriteria: [
        "Demonstrates understanding of framework concepts",
        "Shows practical implementation experience",
        "Explains best practices and patterns"
      ],
      experienceLevel: experience,
      satisfied: false,
      satisfactionScore: 0,
      evidence: [],
      lastAssessed: null
    },
    {
      id: "req_problem_solving_1",
      parameter: "Problem solving",
      category: "problem_solving",
      priority: "high",
      assessmentCriteria: [
        "Describes systematic approach to problem-solving",
        "Shows logical thinking process",
        "Demonstrates ability to break down complex problems"
      ],
      experienceLevel: experience,
      satisfied: false,
      satisfactionScore: 0,
      evidence: [],
      lastAssessed: null
    },
    {
      id: "req_technical_3",
      parameter: "Code quality practices",
      category: "technical",
      priority: "medium",
      assessmentCriteria: [
        "Understands clean code principles",
        "Shows knowledge of testing practices",
        "Demonstrates code review experience"
      ],
      experienceLevel: experience,
      satisfied: false,
      satisfactionScore: 0,
      evidence: [],
      lastAssessed: null
    },
    {
      id: "req_behavioral_2",
      parameter: "Learning adaptability",
      category: "behavioral",
      priority: "medium",
      assessmentCriteria: [
        "Shows willingness to learn new technologies",
        "Demonstrates ability to adapt to change",
        "Provides examples of continuous learning"
      ],
      experienceLevel: experience,
      satisfied: false,
      satisfactionScore: 0,
      evidence: [],
      lastAssessed: null
    },
    {
      id: "req_communication_2",
      parameter: "Technical documentation",
      category: "communication",
      priority: "medium",
      assessmentCriteria: [
        "Shows ability to document technical solutions",
        "Explains complex concepts to non-technical stakeholders",
        "Demonstrates clear written communication skills"
      ],
      experienceLevel: experience,
      satisfied: false,
      satisfactionScore: 0,
      evidence: [],
      lastAssessed: null
    },
    {
      id: "req_technical_4",
      parameter: "System design thinking",
      category: "technical",
      priority: "medium",
      assessmentCriteria: [
        "Shows understanding of system architecture",
        "Demonstrates scalability considerations",
        "Explains design trade-offs"
      ],
      experienceLevel: experience,
      satisfied: false,
      satisfactionScore: 0,
      evidence: [],
      lastAssessed: null
    },
    {
      id: "req_behavioral_3",
      parameter: "Project ownership",
      category: "behavioral",
      priority: "low",
      assessmentCriteria: [
        "Takes responsibility for project outcomes",
        "Shows initiative in problem-solving",
        "Demonstrates accountability"
      ],
      experienceLevel: experience,
      satisfied: false,
      satisfactionScore: 0,
      evidence: [],
      lastAssessed: null
    }
  ];

  // Return the first targetRequirements from the base requirements
  const fallbackRequirements = baseRequirements.slice(0, targetRequirements);

  return {
    requirements: fallbackRequirements,
    assessmentStrategy: {
      completionThreshold: 0.75,
      minRequiredSatisfied: Math.max(5, Math.floor(targetRequirements * 0.7)), // Dynamic based on requirement count
      focusAreas: ["Technical Skills", "Communication", "Problem Solving"]
    },
    candidateContext: {
      experienceLevel: experience,
      targetRole: jobRole,
      keySkills: skills
    },
    generatedAt: new Date().toISOString(),
    version: '1.0',
    fallback: true
  };
};
