// visume-api/routes/geminiRoutes.js
const express = require("express");
const router = express.Router();
const { GoogleGenerativeAI } = require("@google/generative-ai");


router.post("/assist", async (req, res) => {
  try {
    const { messages } = req.body;
    if (!Array.isArray(messages) || messages.length === 0) {
      return res.status(400).json({ error: "Messages array is required" });
    }
    // Convert messages to Gemini SDK format
    const geminiMessages = messages.map((msg) => ({
      role: msg.role,
      parts: [{ text: msg.text }],
    }));
    const genAI = new GoogleGenerativeAI(process.env.GOOGLE_API_KEY);
    const model = genAI.getGenerativeModel({ model: "gemini-2.0-flash" });
    const result = await model.generateContent({ contents: geminiMessages });
    const text =
      result?.response?.candidates?.[0]?.content?.parts?.[0]?.text || "";
    res.json({
      candidates: [
        {
          content: {
            parts: [{ text }],
          },
        },
      ],
    });
  } catch (err) {
    console.error("Gemini SDK error:", err.message, err?.response?.data);
    res.status(500).json({ error: "Gemini SDK error", details: err?.response?.data || err.message });
  }
});

module.exports = router;