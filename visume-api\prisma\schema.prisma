generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model analytics {
  interaction_id   Int                         @id @default(autoincrement())
  employer_id      Int?
  profile_id       Int?
  interaction_type analytics_interaction_type?
  timestamp        DateTime?                   @default(now()) @db.DateTime(0)
  employer         employer?                   @relation(fields: [employer_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "fk_employer")
  videoprofile     videoprofile?               @relation(fields: [profile_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "fk_profile")

  @@index([employer_id], map: "fk_employer")
  @@index([profile_id], map: "fk_profile")
}

model company {
  id                  Int        @id @default(autoincrement())
  company_name        String     @db.VarChar(255)
  company_description String?    @db.Text
  company_website     String?    @db.VarChar(255)
  company_logo        String?    @db.Var<PERSON>har(255)
  gst                 String?    @db.VarChar(20)
  superuser           Int?
  created_at          DateTime   @default(now()) @db.Timestamp(0)
  updated_at          DateTime   @default(now()) @db.Timestamp(0)
  employer            employer[]

  @@index([company_name], map: "idx_company_name")
  @@index([company_website], map: "idx_company_website")
}

model employer {
  id               Int                @id @default(autoincrement())
  emp_id           Int
  emp_name         String             @db.VarChar(50)
  emp_email        String             @db.VarChar(50)
  emp_mobile       BigInt
  company_id       Int?
  designation      String?            @db.VarChar(255)
  company_name     String?            @db.VarChar(255)
  created_at       DateTime           @default(now()) @db.Timestamp(0)
  profile_picture  String?            @db.VarChar(255)
  analytics        analytics[]
  user             user               @relation(fields: [emp_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "employer_ibfk_1")
  company          company?           @relation(fields: [company_id], references: [id], onUpdate: NoAction, map: "employer_ibfk_2")
  employerplans    employerplans[]
  employerprofiles employerprofiles[]
  job_descriptions job_descriptions[]

  @@index([company_id], map: "company_id")
  @@index([emp_id], map: "emp_id")
  @@index([designation], map: "idx_employer_designation")
}

model employerplans {
  id          Int       @id @default(autoincrement())
  emp_id      Int
  plan_id     Int
  start_date  DateTime  @default(now()) @db.Timestamp(0)
  end_date    DateTime? @db.Timestamp(0)
  creditsLeft Int?      @default(0)
  employer    employer  @relation(fields: [emp_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "employerplans_ibfk_1")
  plans       plans     @relation(fields: [plan_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "employerplans_ibfk_2")

  @@index([start_date, end_date], map: "idx_employerplans_dates")
  @@index([emp_id], map: "idx_employerplans_empid")
  @@index([plan_id], map: "idx_employerplans_planid")
}

model employerprofiles {
  id              Int                     @id @default(autoincrement())
  emp_id          Int
  video_resume_id Int?
  status          employerprofiles_status
  shortlisted_at  DateTime                @default(now()) @db.Timestamp(0)
  unlocked_at     DateTime?               @db.Timestamp(0)
  employer        employer                @relation(fields: [emp_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "employerprofiles_ibfk_1")
  videoprofile    videoprofile?           @relation(fields: [video_resume_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "fk_video_resume_id")

  @@index([video_resume_id], map: "idx_employerprofiles_candid")
  @@index([shortlisted_at, unlocked_at], map: "idx_employerprofiles_dates")
  @@index([emp_id], map: "idx_employerprofiles_empid")
  @@index([status], map: "idx_employerprofiles_status")
}

model job_descriptions {
  id                 Int       @id @default(autoincrement())
  employer_id        Int
  suggested_profiles Json
  JobDescription     Json
  timestamp          DateTime? @default(now()) @db.DateTime(0)
  employer           employer  @relation(fields: [employer_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "fk_employer_job_descriptions")

  @@index([employer_id], map: "fk_employer_job_descriptions")
}

model jobseeker {
  id                 Int              @id @default(autoincrement())
  cand_id            String           @unique @db.VarChar(20)
  cand_name          String           @db.VarChar(255)
  cand_mobile        String?          @db.VarChar(15)
  cand_email         String           @db.VarChar(100)
  marks              String?          @db.LongText
  gender             jobseeker_gender
  languages_known    String?          @db.Text
  cand_skills        String?          @db.Text
  profile_picture    String?          @db.VarChar(255)
  stripped_resume    String?          @db.Text
  preferred_location String?          @db.VarChar(255)
  created_at         DateTime         @default(now()) @db.Timestamp(0)
  user               user             @relation(fields: [cand_email], references: [email], onDelete: Cascade, onUpdate: NoAction, map: "fk_cand_email")
  jobseekerplans     jobseekerplans[]
  mock_interview     mock_interview[]
  videoprofile       videoprofile[]

  @@index([cand_id], map: "cand_id")
  @@index([cand_email], map: "fk_cand_email")
  @@index([preferred_location], map: "idx_jobseeker_location")
  @@index([cand_mobile], map: "idx_jobseeker_mobile")
  @@index([cand_name], map: "idx_jobseeker_name")
}

model jobseekerplans {
  id                   Int       @id @default(autoincrement())
  cand_id              Int
  plan_id              Int
  start_date           DateTime  @default(now()) @db.Timestamp(0)
  end_date             DateTime? @db.Timestamp(0)
  is_profile_forwarded Boolean?  @default(false)
  is_profile_promoted  Boolean?  @default(false)
  review_count         Int?      @default(0)
  credits              Int?      @default(0)
  jobseeker            jobseeker @relation(fields: [cand_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "jobseekerplans_ibfk_1")
  plans                plans     @relation(fields: [plan_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "jobseekerplans_ibfk_2")

  @@index([cand_id], map: "idx_jobseekerplans_candid")
  @@index([start_date, end_date], map: "idx_jobseekerplans_dates")
  @@index([plan_id], map: "idx_jobseekerplans_planid")
}

model plans {
  id                 Int              @id @default(autoincrement())
  plan_name          String           @db.VarChar(255)
  plan_description   String?          @db.Text
  plan_price         Decimal          @db.Decimal(10, 2)
  plan_duration_days Int
  role               plans_role?
  credits_assigned   Int?
  features           String?          @db.Text
  created_at         DateTime         @default(now()) @db.Timestamp(0)
  updated_at         DateTime         @default(now()) @db.Timestamp(0)
  employerplans      employerplans[]
  jobseekerplans     jobseekerplans[]

  @@index([plan_name], map: "idx_plans_name")
  @@index([role], map: "idx_plans_role")
}

model suggestedjobs {
  id       Int    @id @default(autoincrement())
  title    String @db.VarChar(50)
  company  String @db.VarChar(200)
  openings Int
  url      String @db.VarChar(200)
  image    String @db.VarChar(100)
}

model user {
  id         Int         @id @default(autoincrement())
  email      String      @unique(map: "email") @db.VarChar(255)
  password   String      @db.VarChar(255)
  role       user_role
  created_at DateTime    @default(now()) @db.Timestamp(0)
  updated_at DateTime    @default(now()) @db.Timestamp(0)
  employer   employer[]
  jobseeker  jobseeker[]
}

model videoprofile {
  id               Int                            @id @default(autoincrement())
  cand_id          String                         @db.VarChar(20)
  video_profile_id BigInt
  role             String                         @db.VarChar(255)
  skills           String?                        @db.Text
  job_type         videoprofile_job_type?
  experience_range videoprofile_experience_range?
  salary           String?                        @db.LongText
  questions        String?                        @db.LongText
  video_url        String?                        @db.VarChar(255)
  score            String?                        @db.LongText
  status           videoprofile_status
  created_at       DateTime                       @default(now()) @db.Timestamp(0)
  requirements     String?                        @db.LongText
  analytics        analytics[]
  employerprofiles employerprofiles[]
  jobseeker        jobseeker                      @relation(fields: [cand_id], references: [cand_id], onDelete: Cascade, onUpdate: NoAction, map: "fk_cand_id")

  @@index([cand_id], map: "fk_cand_id")
}

model roles {
  id        Int    @id @default(autoincrement())
  role_name String @db.VarChar(255)
}

model skills {
  id         Int    @id @default(autoincrement())
  skill_name String @db.VarChar(255)
}

model mock_interview {
  id                Int                              @id @default(autoincrement())
  cand_id           String                           @db.VarChar(20)
  mock_interview_id BigInt?
  role              String?                          @db.VarChar(255)
  skills            String?                          @db.Text
  job_type          mock_interview_job_type?
  experience_range  mock_interview_experience_range?
  salary            String?                          @db.LongText
  questions         String?                          @db.LongText
  score             String?                          @db.LongText
  status            mock_interview_status?
  video_url         String?                          @db.VarChar(255)
  created_at        DateTime?                        @db.Timestamp(0)
  jobseeker         jobseeker                        @relation(fields: [cand_id], references: [cand_id], onDelete: Cascade, onUpdate: Restrict, map: "mock_interview_ibfk_1")

  @@index([cand_id], map: "cand_id")
}

model waitlistentry {
  id        Int                    @id @default(autoincrement())
  userType  waitlistentry_userType
  email     String
  phone     String?
  company   String?
  industry  String?
  createdAt DateTime               @default(now()) @db.Timestamp(0)
}

enum analytics_interaction_type {
  view
  click
}

enum employerprofiles_status {
  shortlisted
  unlocked
}

enum user_role {
  jobseeker
  employer
}

enum plans_role {
  js
  emp
}

enum videoprofile_job_type {
  startup
  mid_range
  mnc
}

enum jobseeker_gender {
  male
  female
  other
}

enum videoprofile_experience_range {
  ZeroToOne   @map("0-1")
  TwoToThree  @map("2-3")
  ThreeToFive @map("3-5")
}

enum videoprofile_status {
  active
  inactive
  started
  notsubmitted
}

enum mock_interview_job_type {
  startup
  mnc
  mid_range
}

enum mock_interview_experience_range {
  ZERO_TO_ONE   @map("0-1")
  TWO_TO_THREE  @map("2-3")
  THREE_TO_FIVE @map("3-5")
}

enum mock_interview_status {
  active
  inactive
  start
}

enum waitlistentry_userType {
  JOB_SEEKER
  HIRING
}
