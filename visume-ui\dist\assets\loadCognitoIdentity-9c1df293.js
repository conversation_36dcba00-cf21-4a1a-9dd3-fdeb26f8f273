var me=Object.defineProperty;var he=(e,s,t)=>s in e?me(e,s,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[s]=t;var i=(e,s,t)=>(he(e,typeof s!="symbol"?s+"":s,t),t);import{s as xe,g as we,n as Y,r as Ee,a as ge,E as ve,c as Se,b as be,f as Pe,t as Ie,A as Ce,N as fe,p as Re,d as Ae,e as ke,h as De,i as ze,D as _e,j as Fe,F as $e,k as Ne,S as Ue,l as je,m as Oe,o as He,q as Te,u as Le,v as Me,w as Ge,x as qe,y as Be,z as Ve,B as Je,C as Ke,G as We,H as Xe,I as Ye,J as Qe,K as Ze,L as et,M as tt,O as st,P as nt,Q as ot,R as it,T as rt,U as at,V as dt,W as ct,_ as c,X as Q,Y as lt,Z as pt,$ as u,a0 as Z,a1 as ut,a2 as yt,a3 as C,a4 as mt,a5 as ht,a6 as ee,a7 as te,a8 as se}from"./index-135fced7.js";const xt=e=>{if(e==null)return;let s;if(typeof e=="number")s=e;else if(typeof e=="string")s=xe(e);else if(typeof e=="object"&&e.tag===1)s=e.value;else throw new TypeError("Epoch timestamps must be expressed as floating point numbers or their string representation");if(Number.isNaN(s)||s===1/0||s===-1/0)throw new TypeError("Epoch timestamps must be valid, non-Infinite, non-NaN numerics");return new Date(Math.round(s*1e3))};class wt{async sign(s,t,n){return s}}const R="***SensitiveInformation***",Et=async(e,s,t)=>({operation:we(s).operation,region:await Y(e.region)()||(()=>{throw new Error("expected `region` to be configured for `aws.auth#sigv4`")})()});function gt(e){return{schemeId:"aws.auth#sigv4",signingProperties:{name:"cognito-identity",region:e.region},propertiesExtractor:(s,t)=>({signingProperties:{config:s,context:t}})}}function I(e){return{schemeId:"smithy.api#noAuth"}}const vt=e=>{const s=[];switch(e.operation){case"GetCredentialsForIdentity":{s.push(I());break}case"GetId":{s.push(I());break}case"GetOpenIdToken":{s.push(I());break}case"UnlinkIdentity":{s.push(I());break}default:s.push(gt(e))}return s},St=e=>{const s=Ee(e);return Object.assign(s,{authSchemePreference:Y(e.authSchemePreference??[])})},bt=e=>Object.assign(e,{useDualstackEndpoint:e.useDualstackEndpoint??!1,useFipsEndpoint:e.useFipsEndpoint??!1,defaultSigningName:"cognito-identity"}),ne={UseFIPS:{type:"builtInParams",name:"useFipsEndpoint"},Endpoint:{type:"builtInParams",name:"endpoint"},Region:{type:"builtInParams",name:"region"},UseDualStack:{type:"builtInParams",name:"useDualstackEndpoint"}},Pt="@aws-sdk/client-cognito-identity",It="AWS SDK for JavaScript Cognito Identity Client for Node.js, Browser and React Native",Ct="3.835.0",ft={build:"concurrently 'yarn:build:cjs' 'yarn:build:es' 'yarn:build:types'","build:cjs":"node ../../scripts/compilation/inline client-cognito-identity","build:es":"tsc -p tsconfig.es.json","build:include:deps":"lerna run --scope $npm_package_name --include-dependencies build","build:types":"tsc -p tsconfig.types.json","build:types:downlevel":"downlevel-dts dist-types dist-types/ts3.4",clean:"rimraf ./dist-* && rimraf *.tsbuildinfo","extract:docs":"api-extractor run --local","generate:client":"node ../../scripts/generate-clients/single-service --solo cognito-identity","test:e2e":"yarn g:vitest run -c vitest.config.e2e.ts --mode development","test:e2e:watch":"yarn g:vitest watch -c vitest.config.e2e.ts"},Rt="./dist-cjs/index.js",At="./dist-types/index.d.ts",kt="./dist-es/index.js",Dt=!1,zt={"@aws-crypto/sha256-browser":"5.2.0","@aws-crypto/sha256-js":"5.2.0","@aws-sdk/core":"3.835.0","@aws-sdk/credential-provider-node":"3.835.0","@aws-sdk/middleware-host-header":"3.821.0","@aws-sdk/middleware-logger":"3.821.0","@aws-sdk/middleware-recursion-detection":"3.821.0","@aws-sdk/middleware-user-agent":"3.835.0","@aws-sdk/region-config-resolver":"3.821.0","@aws-sdk/types":"3.821.0","@aws-sdk/util-endpoints":"3.828.0","@aws-sdk/util-user-agent-browser":"3.821.0","@aws-sdk/util-user-agent-node":"3.835.0","@smithy/config-resolver":"^4.1.4","@smithy/core":"^3.5.3","@smithy/fetch-http-handler":"^5.0.4","@smithy/hash-node":"^4.0.4","@smithy/invalid-dependency":"^4.0.4","@smithy/middleware-content-length":"^4.0.4","@smithy/middleware-endpoint":"^4.1.12","@smithy/middleware-retry":"^4.1.13","@smithy/middleware-serde":"^4.0.8","@smithy/middleware-stack":"^4.0.4","@smithy/node-config-provider":"^4.1.3","@smithy/node-http-handler":"^4.0.6","@smithy/protocol-http":"^5.1.2","@smithy/smithy-client":"^4.4.4","@smithy/types":"^4.3.1","@smithy/url-parser":"^4.0.4","@smithy/util-base64":"^4.0.0","@smithy/util-body-length-browser":"^4.0.0","@smithy/util-body-length-node":"^4.0.0","@smithy/util-defaults-mode-browser":"^4.0.20","@smithy/util-defaults-mode-node":"^4.0.20","@smithy/util-endpoints":"^3.0.6","@smithy/util-middleware":"^4.0.4","@smithy/util-retry":"^4.0.6","@smithy/util-utf8":"^4.0.0",tslib:"^2.6.2"},_t={"@aws-sdk/client-iam":"3.835.0","@tsconfig/node18":"18.2.4","@types/chai":"^4.2.11","@types/node":"^18.19.69",concurrently:"7.0.0","downlevel-dts":"0.10.1",rimraf:"3.0.2",typescript:"~5.8.3"},Ft={node:">=18.0.0"},$t={"<4.0":{"dist-types/*":["dist-types/ts3.4/*"]}},Nt=["dist-*/**"],Ut={name:"AWS SDK for JavaScript Team",url:"https://aws.amazon.com/javascript/"},jt="Apache-2.0",Ot={"./dist-es/runtimeConfig":"./dist-es/runtimeConfig.browser"},Ht="https://github.com/aws/aws-sdk-js-v3/tree/main/clients/client-cognito-identity",Tt={type:"git",url:"https://github.com/aws/aws-sdk-js-v3.git",directory:"clients/client-cognito-identity"},Lt={name:Pt,description:It,version:Ct,scripts:ft,main:Rt,types:At,module:kt,sideEffects:Dt,dependencies:zt,devDependencies:_t,engines:Ft,typesVersions:$t,files:Nt,author:Ut,license:jt,browser:Ot,"react-native":{"./dist-es/runtimeConfig":"./dist-es/runtimeConfig.native"},homepage:Ht,repository:Tt},oe="required",a="fn",d="argv",w="ref",L=!0,M="isSet",P="booleanEquals",x="error",y="endpoint",m="tree",A="PartitionResult",k="getAttr",S="stringEquals",G={[oe]:!1,type:"String"},q={[oe]:!0,default:!1,type:"Boolean"},B={[w]:"Endpoint"},ie={[a]:P,[d]:[{[w]:"UseFIPS"},!0]},re={[a]:P,[d]:[{[w]:"UseDualStack"},!0]},r={},b={[w]:"Region"},V={[a]:k,[d]:[{[w]:A},"supportsFIPS"]},ae={[w]:A},J={[a]:P,[d]:[!0,{[a]:k,[d]:[ae,"supportsDualStack"]}]},K=[ie],W=[re],X=[b],Mt={version:"1.0",parameters:{Region:G,UseDualStack:q,UseFIPS:q,Endpoint:G},rules:[{conditions:[{[a]:M,[d]:[B]}],rules:[{conditions:K,error:"Invalid Configuration: FIPS and custom endpoint are not supported",type:x},{conditions:W,error:"Invalid Configuration: Dualstack and custom endpoint are not supported",type:x},{endpoint:{url:B,properties:r,headers:r},type:y}],type:m},{conditions:[{[a]:M,[d]:X}],rules:[{conditions:[{[a]:"aws.partition",[d]:X,assign:A}],rules:[{conditions:[ie,re],rules:[{conditions:[{[a]:P,[d]:[L,V]},J],rules:[{conditions:[{[a]:S,[d]:[b,"us-east-1"]}],endpoint:{url:"https://cognito-identity-fips.us-east-1.amazonaws.com",properties:r,headers:r},type:y},{conditions:[{[a]:S,[d]:[b,"us-east-2"]}],endpoint:{url:"https://cognito-identity-fips.us-east-2.amazonaws.com",properties:r,headers:r},type:y},{conditions:[{[a]:S,[d]:[b,"us-west-1"]}],endpoint:{url:"https://cognito-identity-fips.us-west-1.amazonaws.com",properties:r,headers:r},type:y},{conditions:[{[a]:S,[d]:[b,"us-west-2"]}],endpoint:{url:"https://cognito-identity-fips.us-west-2.amazonaws.com",properties:r,headers:r},type:y},{endpoint:{url:"https://cognito-identity-fips.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:r,headers:r},type:y}],type:m},{error:"FIPS and DualStack are enabled, but this partition does not support one or both",type:x}],type:m},{conditions:K,rules:[{conditions:[{[a]:P,[d]:[V,L]}],rules:[{endpoint:{url:"https://cognito-identity-fips.{Region}.{PartitionResult#dnsSuffix}",properties:r,headers:r},type:y}],type:m},{error:"FIPS is enabled but this partition does not support FIPS",type:x}],type:m},{conditions:W,rules:[{conditions:[J],rules:[{conditions:[{[a]:S,[d]:["aws",{[a]:k,[d]:[ae,"name"]}]}],endpoint:{url:"https://cognito-identity.{Region}.amazonaws.com",properties:r,headers:r},type:y},{endpoint:{url:"https://cognito-identity.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:r,headers:r},type:y}],type:m},{error:"DualStack is enabled but this partition does not support DualStack",type:x}],type:m},{endpoint:{url:"https://cognito-identity.{Region}.{PartitionResult#dnsSuffix}",properties:r,headers:r},type:y}],type:m}],type:m},{error:"Invalid Configuration: Missing Region",type:x}]},Gt=Mt,qt=new ve({size:50,params:["Endpoint","Region","UseDualStack","UseFIPS"]}),Bt=(e,s={})=>qt.get(e,()=>ge(Gt,{endpointParams:e,logger:s.logger}));Se.aws=be;const Vt=e=>({apiVersion:"2014-06-30",base64Decoder:(e==null?void 0:e.base64Decoder)??Pe,base64Encoder:(e==null?void 0:e.base64Encoder)??Ie,disableHostPrefix:(e==null?void 0:e.disableHostPrefix)??!1,endpointProvider:(e==null?void 0:e.endpointProvider)??Bt,extensions:(e==null?void 0:e.extensions)??[],httpAuthSchemeProvider:(e==null?void 0:e.httpAuthSchemeProvider)??vt,httpAuthSchemes:(e==null?void 0:e.httpAuthSchemes)??[{schemeId:"aws.auth#sigv4",identityProvider:s=>s.getIdentityProvider("aws.auth#sigv4"),signer:new Ce},{schemeId:"smithy.api#noAuth",identityProvider:s=>s.getIdentityProvider("smithy.api#noAuth")||(async()=>({})),signer:new wt}],logger:(e==null?void 0:e.logger)??new fe,serviceId:(e==null?void 0:e.serviceId)??"Cognito Identity",urlParser:(e==null?void 0:e.urlParser)??Re,utf8Decoder:(e==null?void 0:e.utf8Decoder)??Ae,utf8Encoder:(e==null?void 0:e.utf8Encoder)??ke}),Jt=e=>{const s=Te(e),t=()=>s().then(Le),n=Vt(e);return{...n,...e,runtime:"browser",defaultsMode:s,bodyLengthChecker:(e==null?void 0:e.bodyLengthChecker)??De,credentialDefaultProvider:(e==null?void 0:e.credentialDefaultProvider)??(o=>()=>Promise.reject(new Error("Credential is missing"))),defaultUserAgentProvider:(e==null?void 0:e.defaultUserAgentProvider)??ze({serviceId:n.serviceId,clientVersion:Lt.version}),maxAttempts:(e==null?void 0:e.maxAttempts)??_e,region:(e==null?void 0:e.region)??Fe("Region is missing"),requestHandler:$e.create((e==null?void 0:e.requestHandler)??t),retryMode:(e==null?void 0:e.retryMode)??(async()=>(await t()).retryMode||Ne),sha256:(e==null?void 0:e.sha256)??Ue,streamCollector:(e==null?void 0:e.streamCollector)??je,useDualstackEndpoint:(e==null?void 0:e.useDualstackEndpoint)??(()=>Promise.resolve(Oe)),useFipsEndpoint:(e==null?void 0:e.useFipsEndpoint)??(()=>Promise.resolve(He))}},Kt=e=>{const s=e.httpAuthSchemes;let t=e.httpAuthSchemeProvider,n=e.credentials;return{setHttpAuthScheme(o){const h=s.findIndex(E=>E.schemeId===o.schemeId);h===-1?s.push(o):s.splice(h,1,o)},httpAuthSchemes(){return s},setHttpAuthSchemeProvider(o){t=o},httpAuthSchemeProvider(){return t},setCredentials(o){n=o},credentials(){return n}}},Wt=e=>({httpAuthSchemes:e.httpAuthSchemes(),httpAuthSchemeProvider:e.httpAuthSchemeProvider(),credentials:e.credentials()}),Xt=(e,s)=>{const t=Object.assign(Me(e),Ge(e),qe(e),Kt(e));return s.forEach(n=>n.configure(t)),Object.assign(e,Be(t),Ve(t),Je(t),Wt(t))};class Ss extends Ke{constructor(...[t]){const n=Jt(t||{});super(n);i(this,"config");this.initConfig=n;const o=bt(n),h=We(o),E=Xe(h),f=Ye(E),g=dt(f),v=Qe(g),pe=St(v),ue=Xt(pe,(t==null?void 0:t.extensions)||[]);this.config=ue,this.middlewareStack.use(Ze(this.config)),this.middlewareStack.use(et(this.config)),this.middlewareStack.use(tt(this.config)),this.middlewareStack.use(st(this.config)),this.middlewareStack.use(nt(this.config)),this.middlewareStack.use(ot(this.config)),this.middlewareStack.use(it(this.config,{httpAuthSchemeParametersProvider:Et,identityProviderConfigProvider:async ye=>new rt({"aws.auth#sigv4":ye.credentials})})),this.middlewareStack.use(at(this.config))}destroy(){super.destroy()}}class l extends ct{constructor(s){super(s),Object.setPrototypeOf(this,l.prototype)}}class D extends l{constructor(t){super({name:"InternalErrorException",$fault:"server",...t});i(this,"name","InternalErrorException");i(this,"$fault","server");Object.setPrototypeOf(this,D.prototype)}}class z extends l{constructor(t){super({name:"InvalidParameterException",$fault:"client",...t});i(this,"name","InvalidParameterException");i(this,"$fault","client");Object.setPrototypeOf(this,z.prototype)}}class _ extends l{constructor(t){super({name:"LimitExceededException",$fault:"client",...t});i(this,"name","LimitExceededException");i(this,"$fault","client");Object.setPrototypeOf(this,_.prototype)}}class F extends l{constructor(t){super({name:"NotAuthorizedException",$fault:"client",...t});i(this,"name","NotAuthorizedException");i(this,"$fault","client");Object.setPrototypeOf(this,F.prototype)}}class $ extends l{constructor(t){super({name:"ResourceConflictException",$fault:"client",...t});i(this,"name","ResourceConflictException");i(this,"$fault","client");Object.setPrototypeOf(this,$.prototype)}}class N extends l{constructor(t){super({name:"TooManyRequestsException",$fault:"client",...t});i(this,"name","TooManyRequestsException");i(this,"$fault","client");Object.setPrototypeOf(this,N.prototype)}}class U extends l{constructor(t){super({name:"ResourceNotFoundException",$fault:"client",...t});i(this,"name","ResourceNotFoundException");i(this,"$fault","client");Object.setPrototypeOf(this,U.prototype)}}class j extends l{constructor(t){super({name:"ExternalServiceException",$fault:"client",...t});i(this,"name","ExternalServiceException");i(this,"$fault","client");Object.setPrototypeOf(this,j.prototype)}}class O extends l{constructor(t){super({name:"InvalidIdentityPoolConfigurationException",$fault:"client",...t});i(this,"name","InvalidIdentityPoolConfigurationException");i(this,"$fault","client");Object.setPrototypeOf(this,O.prototype)}}class H extends l{constructor(t){super({name:"DeveloperUserAlreadyRegisteredException",$fault:"client",...t});i(this,"name","DeveloperUserAlreadyRegisteredException");i(this,"$fault","client");Object.setPrototypeOf(this,H.prototype)}}class T extends l{constructor(t){super({name:"ConcurrentModificationException",$fault:"client",...t});i(this,"name","ConcurrentModificationException");i(this,"$fault","client");Object.setPrototypeOf(this,T.prototype)}}const Yt=e=>({...e,...e.Logins&&{Logins:R}}),Qt=e=>({...e,...e.SecretKey&&{SecretKey:R}}),Zt=e=>({...e,...e.Credentials&&{Credentials:Qt(e.Credentials)}}),es=e=>({...e,...e.Logins&&{Logins:R}}),ts=async(e,s)=>{const t=le("GetCredentialsForIdentity");let n;return n=JSON.stringify(c(e)),ce(s,t,"/",void 0,n)},ss=async(e,s)=>{const t=le("GetId");let n;return n=JSON.stringify(c(e)),ce(s,t,"/",void 0,n)},ns=async(e,s)=>{if(e.statusCode>=300)return de(e,s);const t=await Q(e.body,s);let n={};return n=ws(t),{$metadata:p(e),...n}},os=async(e,s)=>{if(e.statusCode>=300)return de(e,s);const t=await Q(e.body,s);let n={};return n=c(t),{$metadata:p(e),...n}},de=async(e,s)=>{const t={...e,body:await lt(e.body,s)},n=pt(e,t.body);switch(n){case"InternalErrorException":case"com.amazonaws.cognitoidentity#InternalErrorException":throw await ds(t);case"InvalidParameterException":case"com.amazonaws.cognitoidentity#InvalidParameterException":throw await ls(t);case"LimitExceededException":case"com.amazonaws.cognitoidentity#LimitExceededException":throw await ps(t);case"NotAuthorizedException":case"com.amazonaws.cognitoidentity#NotAuthorizedException":throw await us(t);case"ResourceConflictException":case"com.amazonaws.cognitoidentity#ResourceConflictException":throw await ys(t);case"TooManyRequestsException":case"com.amazonaws.cognitoidentity#TooManyRequestsException":throw await hs(t);case"ResourceNotFoundException":case"com.amazonaws.cognitoidentity#ResourceNotFoundException":throw await ms(t);case"ExternalServiceException":case"com.amazonaws.cognitoidentity#ExternalServiceException":throw await as(t);case"InvalidIdentityPoolConfigurationException":case"com.amazonaws.cognitoidentity#InvalidIdentityPoolConfigurationException":throw await cs(t);case"DeveloperUserAlreadyRegisteredException":case"com.amazonaws.cognitoidentity#DeveloperUserAlreadyRegisteredException":throw await rs(t);case"ConcurrentModificationException":case"com.amazonaws.cognitoidentity#ConcurrentModificationException":throw await is(t);default:const o=t.body;return Es({output:e,parsedBody:o,errorCode:n})}},is=async(e,s)=>{const t=e.body,n=c(t),o=new T({$metadata:p(e),...n});return u(o,t)},rs=async(e,s)=>{const t=e.body,n=c(t),o=new H({$metadata:p(e),...n});return u(o,t)},as=async(e,s)=>{const t=e.body,n=c(t),o=new j({$metadata:p(e),...n});return u(o,t)},ds=async(e,s)=>{const t=e.body,n=c(t),o=new D({$metadata:p(e),...n});return u(o,t)},cs=async(e,s)=>{const t=e.body,n=c(t),o=new O({$metadata:p(e),...n});return u(o,t)},ls=async(e,s)=>{const t=e.body,n=c(t),o=new z({$metadata:p(e),...n});return u(o,t)},ps=async(e,s)=>{const t=e.body,n=c(t),o=new _({$metadata:p(e),...n});return u(o,t)},us=async(e,s)=>{const t=e.body,n=c(t),o=new F({$metadata:p(e),...n});return u(o,t)},ys=async(e,s)=>{const t=e.body,n=c(t),o=new $({$metadata:p(e),...n});return u(o,t)},ms=async(e,s)=>{const t=e.body,n=c(t),o=new U({$metadata:p(e),...n});return u(o,t)},hs=async(e,s)=>{const t=e.body,n=c(t),o=new N({$metadata:p(e),...n});return u(o,t)},xs=(e,s)=>Z(e,{AccessKeyId:C,Expiration:t=>mt(xt(ht(t))),SecretKey:C,SessionToken:C}),ws=(e,s)=>Z(e,{Credentials:t=>xs(t),IdentityId:C}),p=e=>({httpStatusCode:e.statusCode,requestId:e.headers["x-amzn-requestid"]??e.headers["x-amzn-request-id"]??e.headers["x-amz-request-id"],extendedRequestId:e.headers["x-amz-id-2"],cfId:e.headers["x-amz-cf-id"]}),Es=ut(l),ce=async(e,s,t,n,o)=>{const{hostname:h,protocol:E="https",port:f,path:g}=await e.endpoint(),v={protocol:E,hostname:h,port:f,method:"POST",path:g.endsWith("/")?g.slice(0,-1)+t:g+t,headers:s};return n!==void 0&&(v.hostname=n),o!==void 0&&(v.body=o),new yt(v)};function le(e){return{"content-type":"application/x-amz-json-1.1","x-amz-target":`AWSCognitoIdentityService.${e}`}}class bs extends ee.classBuilder().ep(ne).m(function(s,t,n,o){return[te(n,this.serialize,this.deserialize),se(n,s.getEndpointParameterInstructions())]}).s("AWSCognitoIdentityService","GetCredentialsForIdentity",{}).n("CognitoIdentityClient","GetCredentialsForIdentityCommand").f(Yt,Zt).ser(ts).de(ns).build(){}class Ps extends ee.classBuilder().ep(ne).m(function(s,t,n,o){return[te(n,this.serialize,this.deserialize),se(n,s.getEndpointParameterInstructions())]}).s("AWSCognitoIdentityService","GetId",{}).n("CognitoIdentityClient","GetIdCommand").f(es,void 0).ser(ss).de(os).build(){}export{Ss as CognitoIdentityClient,bs as GetCredentialsForIdentityCommand,Ps as GetIdCommand};
